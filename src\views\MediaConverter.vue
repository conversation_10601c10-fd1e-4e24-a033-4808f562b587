<template>
  <div class="p-4 bg-gray-100 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">Media Converter</h2>
    
    <div class="space-y-4">
      <div>
        <label class="block mb-2">Conversion Type</label>
        <select 
          v-model="conversionType" 
          class="w-full p-2 border rounded"
        >
          <option value="audio">Audio (MP3/WAV)</option>
          <option value="video">Video (Resize/Convert)</option>
          <option value="image">Image Conversion</option>
          <option value="videoImage">Video to Image Conversion</option>
        </select>
      </div>

      <div v-if="conversionType !== 'videoImage'">
        <label class="block mb-2">Output Format</label>
        <select 
          v-model="outputFormat" 
          class="w-full p-2 border rounded"
        >
          <template v-if="conversionType === 'audio'">
            <option value="mp3">MP3</option>
            <option value="wav">WAV</option>
          </template>
          <template v-else-if="conversionType === 'video'">
            <option value="mp4">MP4</option>
            <option value="720p">720p</option>
            <option value="1080p">1080p</option>
            <option value="blurred">Blurred Sides</option>
          </template>
          <template v-else-if="conversionType === 'image'">
            <option value="png">PNG</option>
            <option value="jpg">JPG</option>
          </template>
        </select>
      </div>
      <div v-if="conversionType === 'videoImage'">
        <label class="block mb-2">Number of frame: 1-60</label>
        <input 
          v-model="numberOfFrames" 
          class="w-full p-2 border rounded"
          type="number"
          min="1"
        >
      </div>
      <div class="flex space-x-4">
        <button 
          @click="selectFiles" 
          class="flex-1 bg-blue-500 text-white p-2 rounded hover:bg-blue-600"
        >
          Select Files
        </button>
        <button 
          @click="startConversion" 
          :disabled="!canConvert"
          class="flex-1 bg-green-500 text-white p-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          Convert
        </button>
        <button 
          @click="stopConversion" 
          v-if="isCovert"
          class="flex-1 bg-green-500 text-white p-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          Stop
        </button>
      </div>

      <div v-if="inputFiles.length" class="mt-4">
        <h3 class="font-semibold mb-2">Selected Files:</h3>
        <ul class="bg-white border rounded p-2">
          <li 
            v-for="file in inputFiles" 
            :key="file" 
            class="truncate"
          >
            {{ file }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ConversionService } from '@/services/conversionService'
const { ipcRenderer } = require('electron');
const emit = defineEmits<{
  (e: 'set-title', title: string): void
}>()

const job_id =ref(null)

onMounted(async() => {
  emit('set-title', 'Media Converter')
    ipcRenderer.on('job_id', (e, event) => {
        job_id.value = event;
    })
})
onUnmounted(() => {

})


const numberOfFrames = ref(1)

const conversionType = ref<'audio' | 'video' | 'image'>('audio')
const outputFormat = ref('mp3')
const inputFiles = ref<string[]>([])
const isCovert = ref<boolean>(false)

const canConvert = computed(() => inputFiles.value.length > 0)

async function selectFiles() {
  try {
    const files = await ConversionService.selectInputFiles()
    inputFiles.value = files
  } catch (error) {
    console.error('File selection error:', error)
  }
}

async function startConversion() {
  try {
    isCovert.value = true
    const result = await ConversionService.convert({
      type: conversionType.value,
      outputFormat: outputFormat.value,
      inputFiles: inputFiles.value,
      numberOfFrames: numberOfFrames.value
    })
    
    // Pass the conversion type to open the correct folder
    await ConversionService.openOutputFolder(conversionType.value)
    console.log("Conversion Results:", result);
    inputFiles.value = [] // Reset files after conversion
    isCovert.value = false
  } catch (error) {
    console.error('Conversion error:', error)
    // Optional: Add user-friendly error handling
    alert('Conversion failed: ' + error)
    isCovert.value = false
  } 
}

function stopConversion() {
  ConversionService.stopConvert({jobId: job_id.value})
  isCovert.value = false
}


</script>