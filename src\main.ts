import { createApp } from "vue";
import { createPinia } from 'pinia'
import { toast } from 'vue3-toastify';
import 'vue3-toastify/dist/index.css';
import './style.css'
import router from './router'
import App from "./App.vue";



// define global variables S type

declare global {
    interface Window { 
        S: any;
        cv: any;
     }

     
}







window.S = reactive({})

const app = createApp(App)
const pinia = createPinia()

app.use(router)
app.use(pinia)
app.mount('#app')

window.S.toast = toast