

interface OpenCV {
    // Define OpenCV functions here
    // Example: 
}


function analyzeLogoMovement(flow: { cols: number; data32F: any[]; }, selectionBox: { x: number; y: number; width: number; height: number; }) {
    // Extract logo region from flow
    const x = Math.round(selectionBox.x);
    const y = Math.round(selectionBox.y);
    const w = Math.round(selectionBox.width);
    const h = Math.round(selectionBox.height);
  
    let totalVerticalMovement = 0;
    let pointsTracked = 0;
  
    // Analyze optical flow vectors in logo region
    for (let dy = 0; dy < h; dy++) {
      for (let dx = 0; dx < w; dx++) {
        const index = 2 * ((y + dy) * flow.cols + (x + dx));
        const verticalFlow = flow.data32F[index + 1]; // Vertical component
        
        totalVerticalMovement += verticalFlow;
        pointsTracked++;
      }
    }
  
    // Calculate average vertical movement
    const avgVerticalMovement = totalVerticalMovement / pointsTracked;
  
    // Detect significant upward movement
    return avgVerticalMovement < -1 ? {
      direction: 'up',
      speed: Math.abs(avgVerticalMovement)
    } : null;
  }
  
  // Function to extract logo region (ROI) from the video based on selection box
  function extractLogoRegion(videoElement: CanvasImageSource, selectionBox: { width: number; height: number; x: number; y: number; }) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
  
    // Set canvas size to the selected area's size
    canvas.width = selectionBox.width;
    canvas.height = selectionBox.height;
  
    // Draw the selected region (logo area) from the video onto the canvas
    ctx.drawImage(
      videoElement,
      selectionBox.x,
      selectionBox.y,
      selectionBox.width,
      selectionBox.height,
      0, 0,
      selectionBox.width,
      selectionBox.height
    );
  
    return canvas;
  }
  
  // Function to track logo in the video using template matching (OpenCV)
  function trackLogoWithTemplateMatching(videoElement: CanvasImageSource, logoRegionCanvas: { width: number; height: number; }) {
    // Kiểm tra xem canvas có hợp lệ không
    if (logoRegionCanvas.width <= 0 || logoRegionCanvas.height <= 0) {
      console.error('Logo region canvas is invalid.');
      return [];
    }
  
    // Đọc mẫu logo từ canvas
    const mat = cv.imread(logoRegionCanvas); 
  
    // Kiểm tra xem mat có hợp lệ không
    if (mat.empty()) {
      console.error('Failed to read logo region into OpenCV.');
      return [];
    }
  
    const videoCanvas = document.createElement('canvas');
    const videoCtx = videoCanvas.getContext('2d')
  
    // Kiểm tra xem video có sẵn và hợp lệ không
    if (videoElement.readyState >= 2) {  // Video đã sẵn sàng
      videoCtx.drawImage(videoElement, 0, 0, videoElement.videoWidth, videoElement.videoHeight);
      const videoMat = cv.imread(videoCanvas);
  
      // Kiểm tra xem videoMat có hợp lệ không
      if (videoMat.empty()) {
        console.error('Failed to read video frame into OpenCV.');
        return [];
      }
  
      try {
        const result = new cv.Mat();
        cv.matchTemplate(videoMat, mat, result, cv.TM_CCOEFF_NORMED);
  
        const minMax = cv.minMaxLoc(result);
        const topLeft = minMax.maxLoc;
  
        const rect = {
          x: topLeft.x,
          y: topLeft.y,
          width: logoRegionCanvas.width,
          height: logoRegionCanvas.height,
        };
  
        mat.delete();
        videoMat.delete();
        result.delete();
  
        return [rect];
      } catch (error) {
        console.error('Error during template matching:', error);
      }
  
    } else {
      console.error('Video not ready to be processed.');
      return [];
    }
  }
  








  function detectLogoInVideo2() {
    const videoElement = videoRef.value;
    const initialBox = selectionBoxes.value[0];
    
    if (!initialBox || !window.cv) return;
  
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    let prevDetectedBox = { ...initialBox };
    let prevFrame = null;
  
    videoElement.addEventListener('play', () => {
      const processFrame = () => {
        if (videoElement.paused || videoElement.ended) return;
  
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        ctx.drawImage(videoElement, 0, 0);
  
        const currentMat = cv.imread(canvas);
        
        if (prevFrame) {
          const searchRegion = new cv.Rect(
            Math.max(0, prevDetectedBox.x - 50),
            Math.max(0, prevDetectedBox.y - 50),
            Math.min(prevDetectedBox.width + 100, videoElement.videoWidth - prevDetectedBox.x + 50),
            Math.min(prevDetectedBox.height + 100, videoElement.videoHeight - prevDetectedBox.y + 50)
          );
  
          if (searchRegion.width <= 0 || searchRegion.height <= 0) {
            console.log("Logo tracking stopped: outside frame boundaries.");
            prevFrame.delete();
            return; // Stop further processing
          }
  
          const searchMat = currentMat.roi(searchRegion);
          const logoTemplate = prevFrame.roi(new cv.Rect(
            prevDetectedBox.x, 
            prevDetectedBox.y, 
            prevDetectedBox.width, 
            prevDetectedBox.height
          ));
  
          const result = new cv.Mat();
          cv.matchTemplate(searchMat, logoTemplate, result, cv.TM_CCOEFF_NORMED);
  
          const minMax = cv.minMaxLoc(result);
          
          if (minMax.maxVal > 0.6) {
            const detectedLocation = {
              x: searchRegion.x + minMax.maxLoc.x,
              y: searchRegion.y + minMax.maxLoc.y,
              width: initialBox.width,
              height: initialBox.height
            };
  
            if (
                detectedLocation.x < 0 ||
                detectedLocation.y < 0 ||
                detectedLocation.x + detectedLocation.width > videoElement.videoWidth ||
                detectedLocation.y + detectedLocation.height > videoElement.videoHeight
              ) {
                console.log("Detected logo is outside of frame boundaries. Stopping tracking.");
                return; // Dừng xử lý khung hình này
              }
            if (
              detectedLocation.x >= 0 &&
              detectedLocation.y >= 0 &&
              detectedLocation.x + detectedLocation.width <= videoElement.videoWidth &&
              detectedLocation.y + detectedLocation.height <= videoElement.videoHeight
            ) {
              detections.value.push({
                time: videoElement.currentTime,
                rect: detectedLocation
              });
  
              prevDetectedBox = detectedLocation;
            } else {
              console.log("Detected logo outside frame boundaries.");
            }
            
          } else {
            console.log("Logo detection confidence too low.");
          }
  
          searchMat.delete();
          logoTemplate.delete();
          result.delete();
        }
  
        prevFrame = currentMat;
        setTimeout(processFrame, 1000 / 30);
      };
  
      processFrame();
    });
  }
  
  
  function detectLogoInVideo3() {
    const videoElement = videoRef.value;
    const initialBox = selectionBoxes.value[0];
    
    if (!initialBox || !window.cv) return;
  
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    let prevDetectedBox = { ...initialBox };
    let prevFrame = null;
  
    videoElement.addEventListener('play', () => {
      const processFrame = () => {
        if (videoElement.paused || videoElement.ended) return;
  
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        ctx.drawImage(videoElement, 0, 0);
  
        const currentMat = cv.imread(canvas);
        
        if (prevFrame) {
          const searchRegion = new cv.Rect(
            Math.max(0, prevDetectedBox.x - 50),
            Math.max(0, prevDetectedBox.y - 50),
            prevDetectedBox.width + 100,
            prevDetectedBox.height + 100
          );
  
          const searchMat = currentMat.roi(searchRegion);
          const logoTemplate = prevFrame.roi(new cv.Rect(
            prevDetectedBox.x, 
            prevDetectedBox.y, 
            prevDetectedBox.width, 
            prevDetectedBox.height
          ));
  
          const result = new cv.Mat();
          cv.matchTemplate(searchMat, logoTemplate, result, cv.TM_CCOEFF_NORMED);
  
          const minMax = cv.minMaxLoc(result);
          
          if (minMax.maxVal > 0.6) {
            const detectedLocation = {
              x: searchRegion.x + minMax.maxLoc.x,
              y: searchRegion.y + minMax.maxLoc.y,
              width: initialBox.width,
              height: initialBox.height
            };
  
            detections.value.push({
              time: videoElement.currentTime,
              rect: detectedLocation
            });
  
            prevDetectedBox = detectedLocation;
          }
  
          searchMat.delete();
          logoTemplate.delete();
          result.delete();
        }
  
        prevFrame = currentMat;
        setTimeout(processFrame, 1000 / 30);
      };
  
      processFrame();
    });
  }



  function detectLogoInVideo() {
    const videoElement = videoRef.value;
    const initialBox = initBoxes.value[0];
  
    if (!initialBox || !window.cv) return;
  
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
  
    let prevDetectedBox = { ...initialBox };
    let prevFrame = null;
    let consecutiveFailures = 0;
  
    videoElement.addEventListener('play', () => {
      const processFrame = () => {
        if (videoElement.paused || videoElement.ended) return;
  
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        ctx.drawImage(videoElement, 0, 0);
  
        const currentMat = cv.imread(canvas);
  
        if (prevFrame) {
          const searchPadding = Math.max(50 - consecutiveFailures * 10, 20);
          const searchRegion = new cv.Rect(
            Math.max(0, prevDetectedBox.x - searchPadding),
            Math.max(0, prevDetectedBox.y - searchPadding),
            prevDetectedBox.width + searchPadding * 2,
            prevDetectedBox.height + searchPadding * 2
          );
  
          if (
            searchRegion.x + searchRegion.width > currentMat.cols ||
            searchRegion.y + searchRegion.height > currentMat.rows
          ) {
            console.warn("Search region is outside the frame boundaries.");
            currentMat.delete();
            setTimeout(processFrame, 1000 / 30);
            return;
          }
  
          const searchMat = currentMat.roi(searchRegion);
  
          const templateRegion = new cv.Rect(
            prevDetectedBox.x,
            prevDetectedBox.y,
            prevDetectedBox.width,
            prevDetectedBox.height
          );
  
          if (
            templateRegion.x + templateRegion.width > prevFrame.cols ||
            templateRegion.y + templateRegion.height > prevFrame.rows
          ) {
            console.warn("Template region is outside the frame boundaries.");
            searchMat.delete();
            currentMat.delete();
            setTimeout(processFrame, 1000 / 30);
            return;
          }
  
          const logoTemplate = prevFrame.roi(templateRegion);
          const result = new cv.Mat();
          cv.matchTemplate(searchMat, logoTemplate, result, cv.TM_CCOEFF_NORMED);
  
          const minMax = cv.minMaxLoc(result);
          const maxValThreshold = 0.8;
  
          if (minMax.maxVal > maxValThreshold) {
            const detectedLocation = {
              x: searchRegion.x + minMax.maxLoc.x,
              y: searchRegion.y + minMax.maxLoc.y,
              width: initialBox.width,
              height: initialBox.height
            };
  
            detections.value.push({
              time: videoElement.currentTime,
              rect: detectedLocation
            });
            selectionBoxes.value = [detectedLocation]
  
            prevDetectedBox = detectedLocation;
            consecutiveFailures = 0;
          } else {
            consecutiveFailures++;
          }
  
          searchMat.delete();
          logoTemplate.delete();
          result.delete();
        }
  
        if (prevFrame) prevFrame.delete(); // Giải phóng bộ nhớ
        prevFrame = currentMat;
        setTimeout(processFrame, 1000 / 30);
      };
  
      processFrame();
    });
  }


  // 
  function detectLogoInVideo() {
    const videoElement = videoRef.value;
    const initialBox = initBoxes.value[0];
  
    if (!initialBox || !window.cv) {
      console.error('OpenCV hoặc initialBox không khả dụng');
      return;
    }
  
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
  
    let templateHSV = null;
    let isFirstFrame = true;
    let prevDetectedBox = { ...initialBox };
    let consecutiveFailures = 0;
  
    const getColorRange = (templateMat) => {
      if (!templateMat || templateMat.empty()) {
        console.error('Template mat không hợp lệ');
        return null;
      }
  
      const meanColor = cv.mean(templateMat);
      return {
        lower: [
          Math.max(0, meanColor[0] - 20),
          Math.max(0, meanColor[1] - 50),
          Math.max(0, meanColor[2] - 50),
          0
        ],
        upper: [
          Math.min(180, meanColor[0] + 20),
          Math.min(255, meanColor[1] + 50),
          Math.min(255, meanColor[2] + 50),
          255
        ]
      };
    };
  
    videoElement.addEventListener('play', () => {
      const processFrame = () => {
        try {
          if (videoElement.paused || videoElement.ended) return;
  
          // Đảm bảo video đã load
          if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
            requestAnimationFrame(processFrame);
            return;
          }
  
          canvas.width = videoElement.videoWidth;
          canvas.height = videoElement.videoHeight;
          ctx.drawImage(videoElement, 0, 0);
  
          // Đọc frame hiện tại
          let currentMat = cv.imread(canvas);
          if (currentMat.empty()) {
            console.error('Không thể đọc frame');
            return;
          }
  
          // Chuyển đổi sang HSV
          let hsvMat = new cv.Mat();
          cv.cvtColor(currentMat, hsvMat, cv.COLOR_BGR2HSV);
  
          // Xử lý frame đầu tiên
          if (isFirstFrame) {
            if (initialBox.x >= 0 && initialBox.y >= 0 && 
                initialBox.width > 0 && initialBox.height > 0 &&
                initialBox.x + initialBox.width <= hsvMat.cols &&
                initialBox.y + initialBox.height <= hsvMat.rows) {
              
              const templateRegion = new cv.Rect(
                initialBox.x,
                initialBox.y,
                initialBox.width,
                initialBox.height
              );
              
              let tempROI = hsvMat.roi(templateRegion);
              const colorRange = getColorRange(tempROI);
              if (colorRange) {
                templateHSV = colorRange;
                isFirstFrame = false;
              }
              tempROI.delete();
            }
          }
  
          if (!templateHSV) {
            currentMat.delete();
            hsvMat.delete();
            requestAnimationFrame(processFrame);
            return;
          }
  
          // Tạo mask
          let mask = new cv.Mat();
          let lowerMat = new cv.Mat(hsvMat.rows, hsvMat.cols, hsvMat.type(), templateHSV.lower);
          let upperMat = new cv.Mat(hsvMat.rows, hsvMat.cols, hsvMat.type(), templateHSV.upper);
          
          cv.inRange(hsvMat, lowerMat, upperMat, mask);
  
          // Xử lý nhiễu
          let kernel = cv.getStructuringElement(cv.MORPH_RECT, new cv.Size(3, 3));
          let cleanedMask = new cv.Mat();
          cv.morphologyEx(mask, cleanedMask, cv.MORPH_OPEN, kernel);
          cv.morphologyEx(cleanedMask, cleanedMask, cv.MORPH_CLOSE, kernel);
  
          // Tìm contours
          let contours = new cv.MatVector();
          let hierarchy = new cv.Mat();
          cv.findContours(cleanedMask, contours, hierarchy, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);
  
          let bestMatch = null;
          let maxArea = 0;
          const targetAspectRatio = initialBox.width / initialBox.height;
  
          // Tìm contour phù hợp nhất
          for (let i = 0; i < contours.size(); i++) {
            const contour = contours.get(i);
            const area = cv.contourArea(contour);
            const rect = cv.boundingRect(contour);
            
            if (area < 100) continue; // Bỏ qua các contour quá nhỏ
  
            const aspectRatio = rect.width / rect.height;
            const aspectRatioError = Math.abs(aspectRatio - targetAspectRatio);
  
            if (area > maxArea && aspectRatioError < 0.5) {
              maxArea = area;
              bestMatch = rect;
            }
          }
  
          // Xử lý kết quả tìm được
          if (bestMatch) {
            const distance = Math.sqrt(
              Math.pow(bestMatch.x - prevDetectedBox.x, 2) +
              Math.pow(bestMatch.y - prevDetectedBox.y, 2)
            );
  
            if (distance < 100 || consecutiveFailures > 5) {
              detections.value.push({
                time: videoElement.currentTime,
                rect: {
                  x: bestMatch.x,
                  y: bestMatch.y,
                  width: bestMatch.width,
                  height: bestMatch.height
                }
              });
  
              selectionBoxes.value = [{
                x: bestMatch.x,
                y: bestMatch.y,
                width: bestMatch.width,
                height: bestMatch.height,
                startTime: 0,
                endTime: videoElement.duration
              }];
  
              prevDetectedBox = bestMatch;
              consecutiveFailures = 0;
            } else {
              consecutiveFailures++;
            }
          } else {
            consecutiveFailures++;
          }
  
          // Dọn dẹp bộ nhớ
          currentMat.delete();
          hsvMat.delete();
          mask.delete();
          lowerMat.delete();
          upperMat.delete();
          cleanedMask.delete();
          kernel.delete();
          contours.delete();
          hierarchy.delete();
  
          requestAnimationFrame(processFrame);
        } catch (error) {
          console.error('Lỗi xử lý frame:', error);
          requestAnimationFrame(processFrame);
        }
      };
  
      processFrame();
    });
  
  
  }



  //  ok

  
function detectLogoInVideo() {
  const videoElement = videoRef.value;
  const initialBox = initBoxes.value[0];

  if (!initialBox || !window.cv) {
    console.error('OpenCV hoặc initialBox không khả dụng');
    return;
  }

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  let prevDetectedBox = { ...initialBox };
  let prevFrame = null;
  let consecutiveFailures = 0;
  
  const MATCH_THRESHOLD = 0.8;
  const MAX_SEARCH_PADDING = 50;
  const MIN_SEARCH_PADDING = 20;

  const isValidPosition = (rect, videoWidth, videoHeight) => {
    return (
      rect.x >= 0 &&
      rect.y >= 0 &&
      rect.width > 0 &&
      rect.height > 0 &&
      rect.x + rect.width <= videoWidth &&
      rect.y + rect.height <= videoHeight
    );
  };

  videoElement.addEventListener('play', () => {
    const processFrame = () => {
      try {
        if (videoElement.paused || videoElement.ended) return;

        if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
          requestAnimationFrame(processFrame);
          return;
        }

        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        ctx.drawImage(videoElement, 0, 0);

        const currentMat = cv.imread(canvas);
        
        if (prevFrame) {
          const searchPadding = Math.max(
            MAX_SEARCH_PADDING - consecutiveFailures * 10, 
            MIN_SEARCH_PADDING
          );

          const searchRegion = new cv.Rect(
            Math.max(0, prevDetectedBox.x - searchPadding),
            Math.max(0, prevDetectedBox.y - searchPadding),
            Math.min(
              prevDetectedBox.width + searchPadding * 2,
              currentMat.cols - Math.max(0, prevDetectedBox.x - searchPadding)
            ),
            Math.min(
              prevDetectedBox.height + searchPadding * 2,
              currentMat.rows - Math.max(0, prevDetectedBox.y - searchPadding)
            )
          );

          if (searchRegion.width <= 0 || searchRegion.height <= 0) {
            currentMat.delete();
            requestAnimationFrame(processFrame);
            return;
          }

          const searchMat = currentMat.roi(searchRegion);

          const templateRegion = new cv.Rect(
            prevDetectedBox.x,
            prevDetectedBox.y,
            prevDetectedBox.width,
            prevDetectedBox.height
          );

          if (!isValidPosition(templateRegion, prevFrame.cols, prevFrame.rows)) {
            searchMat.delete();
            currentMat.delete();
            requestAnimationFrame(processFrame);
            return;
          }

          const logoTemplate = prevFrame.roi(templateRegion);
          const result = new cv.Mat();
          
          cv.matchTemplate(searchMat, logoTemplate, result, cv.TM_CCOEFF_NORMED);
          const minMax = cv.minMaxLoc(result);

          if (minMax.maxVal > MATCH_THRESHOLD) {
            let detectedLocation = {
              x: searchRegion.x + minMax.maxLoc.x,
              y: searchRegion.y + minMax.maxLoc.y,
              width: initialBox.width,
              height: initialBox.height
            };

            detectedLocation = {
              x: Math.max(1, Math.min(detectedLocation.x, videoElement.videoWidth - detectedLocation.width)),
              y: Math.max(1, Math.min(detectedLocation.y, videoElement.videoHeight - detectedLocation.height)),
              width: Math.min(detectedLocation.width, videoElement.videoWidth - detectedLocation.x),
              height: Math.min(detectedLocation.height, videoElement.videoHeight - detectedLocation.y)
            };

            if (isValidPosition(detectedLocation, videoElement.videoWidth, videoElement.videoHeight)) {
              detections.value.push({
                time: videoElement.currentTime,
                rect: detectedLocation
              });
              
              selectionBoxes.value = [{
                ...detectedLocation,
                startTime: 0,
                endTime: videoElement.duration
              }];

              prevDetectedBox = detectedLocation;
              consecutiveFailures = 0;
            } else {
              consecutiveFailures++;
            }
          } else {
            consecutiveFailures++;
            if (consecutiveFailures > 10) {
              if (isValidPosition(initialBox, videoElement.videoWidth, videoElement.videoHeight)) {
                prevDetectedBox = { ...initialBox };
              }
              consecutiveFailures = 0;
            }
          }

          searchMat.delete();
          logoTemplate.delete();
          result.delete();
        }

        if (prevFrame) prevFrame.delete();
        prevFrame = currentMat;

        requestAnimationFrame(processFrame);
      } catch (error) {
        console.error('Lỗi xử lý frame:', error);
        requestAnimationFrame(processFrame);
      }
    };

    processFrame();
  });

}

