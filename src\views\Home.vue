<!-- src/views/Home.vue -->
<template>
  <div class="container mx-auto p-6">
    <h1 class="text-3xl font-bold mb-6 text-center">Media Converter</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <router-link 
        to="/delogo" 
        class="bg-blue-100 hover:bg-blue-200 p-6 rounded-lg shadow-md transition-all text-center"
      >
        <i class="fas fa-music text-4xl mb-4 block text-blue-600"></i>
        <h2 class="text-xl font-semibold">Logo Remover</h2>
        <p class="text-gray-600">
            Logo Remover
        </p>
      </router-link>
      <router-link 
        to="/detect" 
        class="bg-orange-100 hover:bg-orange-200 p-6 rounded-lg shadow-md transition-all text-center"
      >
        <i class="fas fa-video text-4xl mb-4 block text-orange-600"></i>
        <h2 class="text-xl font-semibold">Logo Tracking Remover</h2>
        <p class="text-gray-600">Logo Tracking Remover V2</p>
      </router-link>
      <router-link 
        to="/media" 
        class="bg-green-100 hover:bg-green-200 p-6 rounded-lg shadow-md transition-all text-center"
      >
        <i class="fas fa-video text-4xl mb-4 block text-green-600"></i>
        <h2 class="text-xl font-semibold">Media Converter</h2>
        <p class="text-gray-600">Resize, convert, and edit video files</p>
      </router-link>


    </div>
  </div>
</template>

<script setup lang="ts">
// Optional: Add any necessary logic
</script>