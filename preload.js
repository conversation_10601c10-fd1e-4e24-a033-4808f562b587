const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// contextBridge.exposeInMainWorld('electron', {
//   openDialog: () => ipcRenderer.invoke('dialog:open'),
//   openFile: () => ipcRenderer.invoke('dialog:openFile'),
//   saveDialog: () => ipcRenderer.invoke('dialog:save'),
//   invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
//   listen: (channel, callback) => ipcRenderer.on(channel, callback),
//   ipcRenderer: ipcRenderer,
//   require: (module) => require(module)
// });

window.addEventListener('DOMContentLoaded', () => {
  const script = document.createElement('script');
  script.src = 'https://docs.opencv.org/4.x/opencv.js';
  script.async = true;
  script.onload = () => {
    console.log('OpenCV loaded:', window.cv);
  };
  document.head.appendChild(script);
});