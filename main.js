const { reactive } = require('vue');

global.ROOT_DIR =__dirname
global.IS_DEV = process.env.NODE_ENV === 'development'
global.S = reactive({})
const { app, BrowserWindow, ipcMain, dialog } = require('electron');

const path = require('path');

const fs = require('fs');

require('./handler/server.js')

const setupIpcHandlers = require('./ipcHandlers');
const userDataPath = app.getPath('userData');
const configDirectory = path.join(userDataPath, 'delogoFolder');

global.configDirectory = configDirectory

if (!fs.existsSync(configDirectory)) {
  // 如果目录不存在，创建它
  fs.mkdirSync(configDirectory, { recursive: true });
}
function createWindow () {
  const mainWindow = new BrowserWindow({
    width: 1366,
    height: 768,
    icon: path.join(__dirname, './dist/icons/icon.ico'),
    webPreferences: {
      // preload: path.join(__dirname, 'preload.js'),
        defaultEncoding: 'UTF-8',
        nodeIntegration: true, // Allows use of Node.js functionality in the renderer process
        contextIsolation: false, // Needs to be set to false to access nodeIntegration in the renderer process
        allowRunningInsecureContent: true, 
        partition: 'persist:delogo-partition',
        webSecurity: !IS_DEV,
    }
  });

  if(IS_DEV){ 
    mainWindow.webContents.openDevTools()
    mainWindow.loadURL("http://localhost:3420/");
  }
  mainWindow.setMenuBarVisibility(false)
  const appPath = path.join(__dirname, './dist/index.html');
  if(!IS_DEV) mainWindow.loadFile(appPath);
  setupIpcHandlers({}, mainWindow);
}
if(IS_DEV){
  require('electron-reload')(path.join(__dirname, 'dist'), {
    electron: path.join(__dirname, 'node_modules', '.bin', 'electron')
  });
}
app.on('ready', createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});


