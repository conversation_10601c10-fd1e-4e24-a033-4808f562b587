const { ipcMain, app, shell, BrowserWindow, session } = require('electron');
const fs = require('fs');
const path = require('path');
const { getVideoInfo, delogoVideo, readVideoFile, createTempFile } = require('./handler/delogo');
const { convertMedia, openOutputFolder } = require('./handler/media');
const dialog = require('electron').dialog;





module.exports = function (db, mainWindow) {
    // Handle open dialog
    ipcMain.handle('dialog:open', async () => {
        const result = await dialog.showOpenDialog({ properties: ['openFile', 'openDirectory'] });
        return result.filePaths;
    });
    
    // Handle save dialog
    ipcMain.handle('dialog:save', async () => {
        const result = await dialog.showSaveDialog();
        return result.filePath;
    });



    ipcMain.handle('dialog:openFile', async (event) => {
        const result = await dialog.showOpenDialog(mainWindow, {
          properties: ['openFile'],
          filters: [
            { name: 'Video Files', extensions: ['*'] }
          ]
        });
    
        if (!result.canceled && result.filePaths.length > 0) {
          return result.filePaths[0];
        }
    });
    ipcMain.handle('dialog:openFiles', async (event) => {
        const result = await dialog.showOpenDialog(mainWindow, {
            properties: ['openFile', 'multiSelections'], // Cho phép chọn nhiều file
            filters: [
                { name: 'Video Files', extensions: ['*'] }
            ]
        });
    
        if (!result.canceled && result.filePaths.length > 0) {
            return result.filePaths; // Trả về danh sách tất cả file đã chọn
        }
    
        return []; // Trả về mảng rỗng nếu không có file nào được chọn
    });
  

    ipcMain.on("file-dropped", (event, filePath) => {
        console.log("File dropped:", filePath);
        // Xử lý file tại đây, ví dụ gửi lại renderer process
        mainWindow.webContents.send("file-received", filePath);
      });

    ipcMain.handle('get_video_info', getVideoInfo);
    ipcMain.handle('delogo-video', delogoVideo);
    ipcMain.handle('read-video-file', readVideoFile);
    ipcMain.handle('create-temp-file', createTempFile);

    // media conversion
    ipcMain.handle("convert_media", convertMedia)
    ipcMain.handle("open_output_folder", openOutputFolder)
    ipcMain.handle("set_path_video", (event, filePath) => {
        S.filePath = filePath
    });

}