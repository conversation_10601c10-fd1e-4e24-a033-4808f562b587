// src/router/index.ts
import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import Home from '@/views/Home.vue'
import LogoDetection from '@/components/LogoDetection.vue'
import MediaConverter from '@/views/MediaConverter.vue'
import Delogo from '@/views/Delogo.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/detect',
    name: 'LogoDetection',
    component: LogoDetection
  },
  {
    path: '/media',
    name: 'MediaConverter',
    component: MediaConverter
  },
  {
    path: '/delogo',
    name: 'Delogo',
    component: Delogo
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router