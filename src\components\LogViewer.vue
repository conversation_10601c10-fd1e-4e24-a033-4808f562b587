<!-- LogViewer.vue -->
<template>
  <div class="log-viewer" v-if="logs.length">
    <div 
      ref="logContainer"
      class="log-container bg-gray-900 text-green-400 p-4 rounded-lg max-h-[300px] overflow-y-auto font-mono text-sm"
    >
      <div 
        v-for="(log, index) in logs" 
        :key="index"
        class="log-entry"
        :class="{
          'text-green-300': log.type === 'info',
          'text-yellow-300': log.type === 'warning',
          'text-red-400': log.type === 'error'
        }"
      >
        <span class="log-timestamp text-gray-500 mr-2">
          {{ formatTime(log.timestamp) }}
        </span>
        {{ log.message }}
      </div>
    </div>
    <div class="log-controls mt-2 flex justify-between items-center">
      <button 
        @click="clearLogs" 
        class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
      >
        Clear Logs
      </button>
      <label class="flex items-center">
        <input 
          type="checkbox" 
          v-model="autoScroll" 
          class="mr-2"
        />
        <span class="text-sm">Auto Scroll</span>
      </label>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
const { ipcRenderer } = require('electron');

const logs = ref([])
const logContainer = ref(null)
const autoScroll = ref(true)

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const clearLogs = () => {
  logs.value = []
}

const addLog = (message, type = 'info') => {
  logs.value.push({
    message,
    type,
    timestamp: Date.now()
  })
}

const scrollToBottom = async () => {
  if (autoScroll.value && logContainer.value) {
    await nextTick()
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

// Theo dõi sự thay đổi logs để cuộn
watch(logs, scrollToBottom)


onMounted(async () => {
  // Lắng nghe log từ FFmpeg
  ipcRenderer.on('ffmpeg-log', (event, data) => {
    const message = data
    let type = 'info'

    if (message.includes('error') || message.includes('Error')) {
      type = 'error'
    } else if (message.includes('warn') || message.includes('Warning')) {
      type = 'warning'
    }

    addLog(message, type)
  })
})

onUnmounted(() => {
  // Hủy lắng nghe khi component bị hủy
  //- ipcRenderer.off('ffmpeg-log')
})
</script>