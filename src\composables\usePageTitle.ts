// src/composables/usePageTitle.ts
import { onMounted, onUnmounted } from 'vue'
import { usePageTitleStore } from '@/stores/pageTitle'

export function usePageTitle(title: string) {
  const pageTitleStore = usePageTitleStore()

  onMounted(() => {
    pageTitleStore.setTitle(title)
  })

  onUnmounted(() => {
    // Optional: Reset to default title
    pageTitleStore.setTitle('Media Converter')
  })
}