import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import Components from "unplugin-vue-components/vite";
import AutoImport from "unplugin-auto-import/vite";
import electron from "vite-plugin-electron";

const host = process.env.TAURI_DEV_HOST;

// https://vitejs.dev/config/
export default defineConfig(async () => ({
  plugins: [
    vue(),
    Components({
      dirs: ["src/components"],
      dts: "types/components.d.ts"
    }),
    // electron({
    //   entry: 'src/main.ts',  // Main process entry
    //   vite: {
    //     build: {
    //       rollupOptions: {
    //         external: ['fs', 'path', 'crypto'],
    //       },
    //     },
    //   },
    // }),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
        /\.md$/ // .md
      ],
      imports: [
        // presets
        "vue",
        "vue-router",
        "pinia",

        {
          from: "vue-router",
          imports: ["RouteLocationRaw"],
          type: true
        }
      ],
      dts: "types/auto-imports.d.ts"
    })
  ],
  base: "./",
  resolve: {
    alias: {
      // '@tauri-apps/api': path.resolve(__dirname, 'node_modules/@tauri-apps/api'),
      "@": path.resolve(__dirname, "src"),
      // fs: 'memfs',
      path: "path-browserify",
      crypto: "crypto-browserify"
      // "opencv.js": "/public/opencv.js",
    }
  },
  define: {
    "process.env": {}
  },
  clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 3420,
    strictPort: true,
    host: host || false,
    hmr: host
      ? {
          protocol: "ws",
          host,
          port: 1421
        }
      : undefined,
    watch: {
      // 3. tell vite to ignore watching `src-tauri`
      // ignored: ["**/src-tauri/**"],
    }
  },
  build: {
    emptyOutDir: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["vue", "vue-router", "pinia", "@techstark/opencv-js"]
        }
      }
    }
  }
}));
