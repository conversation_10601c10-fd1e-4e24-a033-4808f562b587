<template>
  <div class="min-h-screen bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
      <!-- Loading Spinner -->
      <div
        v-if="loading"
        class="fixed inset-0 flex items-center justify-center bg-gray-700 bg-opacity-50 z-50"
      >
        <div
          class="animate-spin border-t-4 border-blue-500 border-solid w-16 h-16 rounded-full"
        ></div>
      </div>
      <!-- Video Upload Zone -->
      <div
        @dragover.prevent
        @drop.prevent
        class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors"
      >
        <div v-if="!videoUrl" class="space-y-4 p-8">
          <div class="text-gray-500">
            Kéo thả video vào đây hoặc
            <label
              class="text-blue-500 hover:text-blue-600 cursor-pointer"
              @click="handleFileSelect"
            >
              chọn file
            </label>
          </div>
        </div>

        <!-- Video Container -->
        <div v-else class="relative inline-block">
          <!-- Video Element -->
          <video
            ref="videoRef"
            class="max-w-full"
            @loadedmetadata="handleVideoLoad"
            @timeupdate="updateProgress"
            @error="handleVideoError"
            @loadstart="() => console.log('Video load started')"
            @waiting="() => console.log('Video waiting')"
            @stalled="() => console.log('Video stalled')"
            @suspend="() => console.log('Video suspended')"
            :src="`${videoPath}`"
          />

          <!-- Selection Overlay -->
          <div
            v-if="showSelectionBox"
            ref="overlayRef"
            class="absolute top-0 left-0 w-full h-full cursor-crosshair"
            @mousedown="startDrawing"
            @mousemove="draw"
            @mouseup="endDrawing"
            @mousemove.prevent="
              isDraggingBox
                ? moveBox($event)
                : isResizing
                ? resizeBox($event)
                : null
            "
            @mouseup.prevent="
              isDraggingBox ? endBoxMove() : isResizing ? endResize() : null
            "
          >
            <!-- Current Selection -->
            <div
              v-if="currentBox"
              class="absolute border-2 border-blue-500 bg-blue-500 bg-opacity-20"
              :style="{
                left: `${currentBox.x}px`,
                top: `${currentBox.y}px`,
                width: `${currentBox.width}px`,
                height: `${currentBox.height}px`
              }"
            ></div>

            <!-- Existing Selection Boxes -->
            <div
              v-for="(box, index) in selectionBoxes"
              :key="index"
              class="absolute border-2 border-red-500 bg-red-500 bg-opacity-20"
              :class="{ 'border-green-500 bg-green-500': box.active }"
              :style="{
                left: `${box.x}px`,
                top: `${box.y}px`,
                width: `${box.width}px`,
                height: `${box.height}px`
              }"
            >
              <!-- Di chuyển toàn bộ rectangle -->
              <div
                @mousedown.prevent="startBoxMove(index, $event)"
                class="absolute inset-0 cursor-move"
              ></div>

              <!-- Các điểm resize -->
              <div
                @mousedown.prevent="startResize(index, 'top-left', $event)"
                class="absolute top-0 left-0 w-2 h-2 bg-orange-500 cursor-nwse-resize rounded-br-full"
              ></div>
              <div
                @mousedown.prevent="startResize(index, 'top-right', $event)"
                class="absolute top-0 right-0 w-2 h-2 bg-orange-500 cursor-nesw-resize rounded-bl-full"
              ></div>
              <div
                @mousedown.prevent="startResize(index, 'bottom-left', $event)"
                class="absolute bottom-0 left-0 w-2 h-2 bg-orange-500 cursor-nesw-resize rounded-tr-full"
              ></div>
              <div
                @mousedown.prevent="startResize(index, 'bottom-right', $event)"
                class="absolute bottom-0 right-0 w-2 h-2 bg-orange-500 cursor-nwse-resize rounded-tl-full"
              ></div>

              <!-- Nút xóa -->
              <button
                @click.stop="removeBox(index)"
                class="absolute -top-3 -right-3 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center"
              >
                ×
              </button>

              <!-- Thông tin box -->
              <div
                class="absolute -bottom-6 left-0 text-xs bg-black text-white px-2 py-1 rounded opacity-50"
              >
                {{ Math.round(getVideoCoords(box).x) }},{{
                  Math.round(getVideoCoords(box).y)
                }}
                {{ Math.round(getVideoCoords(box).width) }}x{{
                  Math.round(getVideoCoords(box).height)
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Custom Timeline Slider -->
      <div v-if="videoUrl" class="mt-2 w-full">
        <div
          ref="sliderRef"
          class="relative w-full h-5 bg-gray-200 rounded cursor-pointer"
          @click="seekVideo"
          @mousedown="startDragging"
        >
          <div
            class="absolute left-0 top-0 h-full bg-blue-500 rounded"
            :style="{ width: `${data.progress}%` }"
          ></div>
        </div>

        <!-- Time Display -->
        <div class="flex justify-between text-sm text-gray-600 mt-1  select-none">
          <span>{{ formatTime(data.currentTime) }}</span>
          <span>{{ formatTime(data.duration) }}</span>
        </div>
      </div>
      <!-- Controls -->
      <div v-if="videoUrl" class="mt-6 space-y-4">
        <!-- Progress Bar -->
        <div v-if="processing" class="w-full bg-gray-200 rounded-full h-2.5">
          <div
            class="bg-blue-600 h-2.5 rounded-full"
            :style="{ width: `${progress}%` }"
          ></div>
          <div class="text-xs text-gray-500 ml-2">{{ progress }}%</div>
        </div>
        <div class="flex gap-4 select-none" >
          <button
            @click="toggleSelectionBox"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {{ showSelectionBox ? "Xong chọn vùng" : "Chọn vùng" }}
          </button>

          <!-- Control Panel -->
          <div
            class="flex items-center justify-center space-x-4 border-2 border-gray-300 rounded-lg p-1"
          >
            <button
              @click="togglePlay"
              class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
            >
              {{ data.isPlaying ? "Pause" : "Play" }}
            </button>
            <button
              @click="toggleMute"
              class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              {{ data.isMuted ? "Unmute" : "Mute" }}
            </button>
          </div>
          <button
            @click="toggleAudio"
            class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            :class="{ 'bg-yellow-500 hover:bg-yellow-600': isAudio }"
          >
            {{ isAudio ? "Audio On" : "Audio Off" }}
          </button>
          <button
            @click="processVideo"
            :disabled="processing || selectionBoxes.length === 0"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {{ processing ? "Đang xử lý..." : "Xử lý video" }}
          </button>
          <button
            @click="resetVideo"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            Reset
          </button>
        </div>

        <!-- Selection Info -->

        <div v-if="selectionBoxes.length > 0" class="mt-4">
          <h3 class="font-semibold mb-2">Vùng đã chọn:</h3>
          <div
            v-for="(box, index) in selectionBoxes"
            :key="index"
            class="flex items-center gap-4 mb-2 border-2 border-gray-300 rounded p-1"
          >
            <div class="flex items-center gap-4">
              <span>Vùng {{ index + 1 }}: </span>
              <span>X: {{ Math.round(getVideoCoords(box).x) }}, </span>
              <span>Y: {{ Math.round(getVideoCoords(box).y) }}, </span>
              <span>W: {{ Math.round(getVideoCoords(box).width) }}, </span>
              <span>H: {{ Math.round(getVideoCoords(box).height) }}</span>
              <button
                @click="removeBox(index)"
                class="text-red-500 hover:text-red-600"
              >
                Xóa
              </button>
            </div>
            <label class="flex items-center">
              Start Time:
              <input
                type="text"
                :value="box.startTime"
                @change="updateBoxTime(index, box.startTime, box.endTime)"
                class="ml-2 w-20 border rounded px-2 py-1"
                step="0.1"
                min="0"
              />
              <button
                @click="captureTime(index, 'startTime')"
                class="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
              >
                Start
              </button>
            </label>
            <label class="flex items-center">
              End Time:
              <input
                type="text"
                :value="box.endTime"
                @change="updateBoxTime(index, box.startTime, box.endTime)"
                class="ml-2 w-20 border rounded px-2 py-1"
                step="0.1"
                :min="box.startTime"
              />
              <button
                @click="captureTime(index, 'endTime')"
                class="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
              >
                End
              </button>
            </label>
          </div>
        </div>
        <div v-if="savePath" class="mt-4">Filename: {{ savePath }}</div>
        <LogViewer />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, onUnmounted } from "vue";
import LogViewer from "@/components/LogViewer.vue";
import { VideoProcessor } from "@/services/videoProcession.ts";

const { ipcRenderer } = require("electron");

// State
const videoUrl = ref(null);
const videoRef = ref(null);
const overlayRef = ref(null);
const showSelectionBox = ref(false);
const isDrawing = ref(false);
const selectionBoxes = ref([
  // Thêm các thuộc tính startTime và endTime cho mỗi box
  {
    x: 0,
    y: 0,
    width: 100,
    height: 100,
    startTime: 0, // Thời điểm bắt đầu xuất hiện
    endTime: 0, // Thời điểm kết thúc
    active: true // Trạng thái hiển thị
  }
]);
const currentBox = ref(null);
const processing = ref(false);
const progress = ref(0);
const savePath = ref("");
const videoPath = ref("");
const sliderRef = ref(null);
const isDragging = ref(false);
// Video dimensions
const videoWidth = ref(0);
const videoHeight = ref(0);
const loading = ref(false);

const data = reactive({
  progress: 0,
  currentTime: 0,
  duration: 0,
  isPlaying: false,
  isMuted: false
});

let unlistenDr = null;
let unlistenLog = null;
// Initialize

// Define emit for TypeScript
const emit = defineEmits();

S.delogoSelectionBoxes = selectionBoxes;

const isAudio = ref(true); // Mặc định bật audio




// Thêm function toggle
const toggleAudio = () => {
  isAudio.value = !isAudio.value;
};

onMounted(async () => {
  try {
    emit("set-title", "Video Logo Remover");
    //- ipcRenderer.on('tauri://drag-drop', event => {
    //-   data.progress = 0
    //-   handleFile(event.payload.paths[0])
    //- })
    //- window.addEventListener("dragover", (event) => {
    //-   event.preventDefault(); // Ngăn trình duyệt mở file
    //- });

    //- window.addEventListener("drop", (event) => {
    //-   event.preventDefault();

    //-   if (event.dataTransfer.files.length > 0) {
    //-     const filePath = event.dataTransfer.files[0].path;
    //-     ipcRenderer.send("file-dropped", filePath);
    //-   }
    //- });
    document.addEventListener("dragover", (e) => {
      e.preventDefault();
      e.stopPropagation();
    });

    document.addEventListener("drop", (event) => {
      event.preventDefault();
      event.stopPropagation();
      handleFile(event.dataTransfer.files[0].path);
    });

    ipcRenderer.on("ffmpeg-progress", (event, data) => {
      const progressValue = data;

      if (progressValue === 100) {
        if (progress.value < 99) {
          console.warn("Unexpected completion detected.");
        }
        processing.value = false;
        progress.value = 100;
        S.toast.success("Video processed successfully!");
      } else if (progressValue === -1) {
        // Xử lý lỗi
        progress.value = 0;
        S.toast.error("An error occurred during processing!");
        processing.value = false;
      } else {
        // Cập nhật tiến trình
        progress.value = progressValue;
      }
    });
    if (videoRef.value) {
      videoRef.value.addEventListener("timeupdate", updateVideoTimeDisplay);
    }
    console.log("_loaded");
  } catch (error) {
    console.error("Failed to load:", error);
  }
});

onUnmounted(() => {
  document.removeEventListener('mousemove', onDragging);
  document.removeEventListener('mouseup', stopDragging);

  if (videoRef.value) {
    videoRef.value.removeEventListener("timeupdate", updateVideoTimeDisplay);
  }
});

const resetVideo = () => {
  videoUrl.value = null;
  videoRef.value = null;
  overlayRef.value = null;
  showSelectionBox.value = false;
  isDrawing.value = false;
  selectionBoxes.value = [];
  currentBox.value = null;
  processing.value = false;
  progress.value = 0;
  savePath.value = "";
  videoPath.value = "";
  data.progress = 0;
};

const playAudio = () => {
  if (videoRef.value) {
    videoRef.value.play();
  } else {
    console.log("Video not loaded");
  }
  console.log([videoRef.value]);
};

const handleDrop = (event) => {
  const file = event.dataTransfer.files[0];
  console.log("File", file);
  if (file && file.type.startsWith("video/")) {
    handleFile(file);
  }
};

const handleFileSelect = async (event) => {
  const filePaths = await ipcRenderer.invoke("dialog:openFile");
  console.log(filePaths);
  //-   const selected = await open({
  //-   filters: [{
  //-     name: 'Video',
  //-     extensions: ['mp4', 'avi', 'mkv']
  //-   }]
  //- })
  handleFile(filePaths);
};

const videoInfo = ref(null);

const handleFile = async (selected) => {
  resetVideo();
  console.log("selected", selected);
  loading.value = true;
  if (selected) {
    const randomString = Math.random().toString(36).substring(2, 8); // Tạo chuỗi ngẫu nhiên
    const pathParts = selected.split(/[/\\]/); // Tách các phần từ đường dẫn (Windows hoặc Unix)
    const fullFileName = pathParts.pop(); // Lấy tên file gốc
    const fileDirectory = pathParts.join("/"); // Lấy thư mục

    const [fileName, extension] = fullFileName.split(/\.(?=[^\.]+$)/); // Tách tên và phần mở rộng
    const newFileName = `${fileName}-delogo-${randomString}.${extension}`; // Gắn tên mới
    savePath.value = `${fileDirectory}/${newFileName}`; // Tạo đường dẫn mới
    videoPath.value = selected;
    videoInfo.value = await ipcRenderer.invoke("get_video_info", selected);
    console.log("Original file path:", selected);
    console.log("New file path:", savePath.value);
    console.log("videoInfo.value:", videoInfo.value);
    //- const videoData = await invoke("read_video_file", { path: selected });
    //- const tempFilePath = await invoke('create_temp_file', { path: selected });

    // Tạo Blob từ file tạm (có thể sử dụng fetch hoặc File API nếu cần)
    //- const response = await fetch(`file://${tempFilePath}`);
    //- const videoData = await response.arrayBuffer();
    //- const response = await fetch(`file://${selected}`)
    //- const videoData = await response.arrayBuffer()
    //- const blob = new Blob([new Uint8Array(videoData)], { type: "video/mp4" });
    //- console.log(URL.createObjectURL(blob), selected)
    videoUrl.value = selected; //URL.createObjectURL(blob);
    selectionBoxes.value = [];
  }
  loading.value = false;
};

const handleVideoError = (error) => {
  videoRef.value.src = 'http://127.0.0.1:3210/video'
  console.error("Error loading video:", error);
  // videoRef.value.play()
};

const handleVideoLoad = () => {
  const video = videoRef.value;
  videoWidth.value = video.videoWidth;
  videoHeight.value = video.videoHeight;
  data.duration = video.duration;
};

const toggleSelectionBox = () => {
  showSelectionBox.value = !showSelectionBox.value;
};

// Get relative coordinates within the video element
const getRelativeCoordinates = (event) => {
  const rect = overlayRef.value.getBoundingClientRect();
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  return { x, y };
};

// Convert display coordinates to video coordinates
const getVideoCoords = (displayBox) => {
  if (!videoRef.value) return { x: 0, y: 0, width: 0, height: 0 };

  const videoElement = videoRef.value;
  const scaleX = videoWidth.value / videoElement.offsetWidth;
  const scaleY = videoHeight.value / videoElement.offsetHeight;

  return {
    x: displayBox.x * scaleX,
    y: displayBox.y * scaleY,
    width: displayBox.width * scaleX,
    height: displayBox.height * scaleY
  };
};

const startDrawing = (event) => {
  const { x, y } = getRelativeCoordinates(event);
  currentBox.value = { x, y, width: 0, height: 0 };
  isDrawing.value = true;
};

const draw = (event) => {
  if (!isDrawing.value || !currentBox.value) return;

  const { x, y } = getRelativeCoordinates(event);

  currentBox.value = {
    ...currentBox.value,
    width: x - currentBox.value.x,
    height: y - currentBox.value.y
  };
};

const endDrawing = () => {
  if (currentBox.value && (currentBox.value.width || currentBox.value.height)) {
    // Normalize negative dimensions
    let { x, y, width, height } = currentBox.value;

    if (width < 0) {
      x += width;
      width = Math.abs(width);
    }

    if (height < 0) {
      y += height;
      height = Math.abs(height);
    }

    if (width > 0 && height > 0) {
      const endTime = videoRef.value.duration;
      selectionBoxes.value.push({ x, y, width, height, startTime: 0, endTime });
    }
  }

  currentBox.value = null;
  isDrawing.value = false;
};

const removeBox = (index) => {
  selectionBoxes.value.splice(index, 1);
};

const getVideoFPS = () => {
  if (!videoRef.value) return 25; // default fallback

  // MediaSource API có thể cung cấp thông tin FPS
  if (videoRef.value.getVideoPlaybackQuality) {
    const quality = videoRef.value.getVideoPlaybackQuality();
    console.log("quality", quality);
    if (quality.totalVideoFrames && quality.totalVideoFrames > 0) {
      return Math.round(quality.totalVideoFrames / videoRef.value.duration);
    }
  }

  // Fallback dựa trên thông số phổ biến
  return videoRef.value.videoWidth > 1920 ? 30 : 25;
};

const processVideo = async () => {
  if (!videoUrl.value || selectionBoxes.value.length === 0) return;

  processing.value = true;
  progress.value = 0;

  try {
    const segments = [];
    const lastTime = videoRef.value.duration;
    const boxes = selectionBoxes.value;
    let filterComplex = "";

    // Sắp xếp boxes theo thời gian bắt đầu
    boxes.sort((a, b) => a.startTime - b.startTime);

    // Tạo segments theo thời gian và gom nhóm các box cùng thời điểm
    let currentTime = 0;
    let currentSegment = null;

    const isAllStartTime0 = boxes.every((box) => box.startTime === 0);
    const isAllEndTimeLastTime = boxes.every((box) => box.endTime === lastTime);
    //- if(isAllStartTime0 && isAllEndTimeLastTime){
    //-   filterComplex = boxes.map(box => {
    //-     const videoCoords = getVideoCoords(box)
    //-     return `delogo=x=${Math.round(videoCoords.x)}:y=${Math.round(videoCoords.y)}:w=${Math.round(videoCoords.width)}:h=${Math.round(videoCoords.height)}`
    //-   }).filter(filter => filter !== null).join(',')
    //- } else {
    const { fps, total_frames } = videoInfo.value;
    const getFrameNumber = (timeInSeconds) => {
      const frame = Math.round(timeInSeconds * fps);
      // Đảm bảo frame number không vượt quá tổng số frame
      return Math.min(frame, total_frames - 1);
    };
    console.log("fps", fps);

    // Tạo filter với enable='between(n,...)'
    filterComplex = boxes
      .map((box, index) => {
        const videoCoords = getVideoCoords(box);
        const startFrame = getFrameNumber(box.startTime);
        const endFrame =
          box.endTime === videoRef.value.duration
            ? total_frames - 1
            : getFrameNumber(box.endTime);

        let enableExpr;
        if (box.startTime === 0 && box.endTime === videoRef.value.duration) {
          enableExpr = "1"; // Luôn bật nếu áp dụng cho toàn bộ video
        } else if (box.startTime === 0) {
          enableExpr = `lte(n,${endFrame})`; // Từ đầu đến frame cụ thể
        } else if (box.endTime === videoRef.value.duration) {
          enableExpr = `gte(n,${startFrame})`; // Từ frame cụ thể đến cuối
        } else {
          enableExpr = `between(n,${startFrame},${endFrame})`; // Giữa khoảng frame
        }

        return `delogo=enable='${enableExpr}':x=${Math.round(
          videoCoords.x
        )}:y=${Math.round(videoCoords.y)}:w=${Math.round(
          videoCoords.width
        )}:h=${Math.round(videoCoords.height)}`;
      })
      .join(",");

    if (isAudio.value) {
      filterComplex = `[0:v]${filterComplex}[outv];[0:a]acopy[outa]`;
    } else {
      filterComplex = `[0:v]${filterComplex}[outv]`;
    }

    console.log("Filter complex:", filterComplex);

    // 6. Gọi VideoProcessor
    const videoInfos = {
      width: videoWidth.value,
      height: videoHeight.value,
      duration: lastTime
    };

    await VideoProcessor.processVideo({
      input: videoPath.value,
      output: savePath.value,
      filters: filterComplex,
      videoInfo: videoInfos,
      detected: "no"
    });
  } catch (error) {
    console.error("Error processing video:", error);
    processing.value = false;
    S.toast.error(`Có lỗi xảy ra khi xử lý video: ${error.toString()}`);
  }
};

const formatTime = (seconds) => {
  // Convert seconds to MM:SS format
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const seekVideo = (event) => {
  if (sliderRef.value && videoRef.value) {
    const slider = sliderRef.value;
    const rect = slider.getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    const percentage = (offsetX / rect.width) * 100;

    // Đảm bảo percentage nằm trong khoảng 0-100
    const clampedPercentage = Math.max(0, Math.min(100, percentage));

    videoRef.value.currentTime =
      (clampedPercentage / 100) * videoRef.value.duration;
  }
};


function togglePlay() {
  const video = videoRef.value;
  if (video.paused) {
    video.play();
    data.isPlaying = true;
  } else {
    video.pause();
    data.isPlaying = false;
  }
}

function toggleMute() {
  const video = videoRef.value;
  video.muted = !video.muted;
  data.isMuted = video.muted;
}

// const startDragging = (event) => {
//   isDragging.value = true;
//   updateVideoTime(event);
// };
const startDragging = (event) => {
  isDragging.value = true;
  updateProgress(event);
  
  // Add event listeners to document
  document.addEventListener('mousemove', onDragging);
  document.addEventListener('mouseup', stopDragging);
};

const onDragging = (event) => {
  if (isDragging.value) {
    updateProgress(event);
  }
};

const stopDragging = () => {
  isDragging.value = false;
  
  // Remove event listeners from document
  document.removeEventListener('mousemove', onDragging);
  document.removeEventListener('mouseup', stopDragging);
};
// const onDragging = (event) => {
//   if (isDragging.value) {
//     updateVideoTime(event);
//   }
// };

// const stopDragging = () => {
//   isDragging.value = false;
// };

// Thêm state để quản lý rectangle đang được chọn để di chuyển
const selectedBoxIndex = ref(null);
const isDraggingBox = ref(false);

const isResizing = ref(false);
const resizeDirection = ref(null);

// Hàm bắt đầu di chuyển rectangle
const startBoxMove = (index, event) => {
  selectedBoxIndex.value = index;
  isDraggingBox.value = true;
  event.stopPropagation(); // Ngăn chặn sự kiện vẽ rectangle mới
};
// Hàm bắt đầu resize
const startResize = (index, direction, event) => {
  selectedBoxIndex.value = index;
  isResizing.value = true;
  resizeDirection.value = direction;
  event.stopPropagation();
};

const moveBox = (event) => {
  if (!isDraggingBox.value || selectedBoxIndex.value === null) return;

  const { x, y } = getRelativeCoordinates(event);
  const videoElement = videoRef.value;
  const currentBox = selectionBoxes.value[selectedBoxIndex.value];

  // Tính toán vị trí mới, đảm bảo cách viền 1px
  const newX = Math.max(
    1,
    Math.min(
      x - currentBox.width / 2,
      videoElement.offsetWidth - currentBox.width - 1
    )
  );

  const newY = Math.max(
    1,
    Math.min(
      y - currentBox.height / 2,
      videoElement.offsetHeight - currentBox.height - 1
    )
  );

  selectionBoxes.value[selectedBoxIndex.value] = {
    ...currentBox,
    x: newX,
    y: newY
  };

  //- initBoxes.value = [selectionBoxes.value[selectedBoxIndex.value]]
};

// Hàm kết thúc di chuyển
const endBoxMove = () => {
  isDraggingBox.value = false;
  selectedBoxIndex.value = null;
};

// Hàm kết thúc resize
const endResize = () => {
  isResizing.value = false;
  selectedBoxIndex.value = null;
};

const resizeBox = (event) => {
  if (!isResizing.value || selectedBoxIndex.value === null) return;

  const { x, y } = getRelativeCoordinates(event);
  const box = selectionBoxes.value[selectedBoxIndex.value];
  const videoElement = videoRef.value;

  // Tính toán giới hạn cho resize
  const minSize = 1;
  const maxX = videoElement.offsetWidth - 1;
  const maxY = videoElement.offsetHeight - 1;

  let newX = box.x;
  let newY = box.y;
  let newWidth = box.width;
  let newHeight = box.height;

  switch (resizeDirection.value) {
    case "top-left":
      newX = Math.max(1, Math.min(x, box.x + box.width - minSize));
      newY = Math.max(1, Math.min(y, box.y + box.height - minSize));
      newWidth = box.x + box.width - newX;
      newHeight = box.y + box.height - newY;
      break;
    case "top-right":
      newWidth = Math.max(minSize, Math.min(x - box.x, maxX - box.x));
      newY = Math.max(1, Math.min(y, box.y + box.height - minSize));
      newHeight = box.y + box.height - newY;
      break;
    case "bottom-left":
      newX = Math.max(1, Math.min(x, box.x + box.width - minSize));
      newWidth = box.x + box.width - newX;
      newHeight = Math.max(minSize, Math.min(y - box.y, maxY - box.y));
      break;
    case "bottom-right":
      newWidth = Math.max(minSize, Math.min(x - box.x, maxX - box.x));
      newHeight = Math.max(minSize, Math.min(y - box.y, maxY - box.y));
      break;
  }

  selectionBoxes.value[selectedBoxIndex.value] = {
    ...box,
    x: newX,
    y: newY,
    width: newWidth,
    height: newHeight
  };
};
const updateProgress = (event) => {
  if (!sliderRef.value) return;
  const video = videoRef.value;
  
  const rect = sliderRef.value.getBoundingClientRect();
  const offsetX = event.clientX - rect.left;
  let progress = (offsetX / rect.width) * 100;
  
  // Clamp progress between 0 and 100
  progress = Math.max(0, Math.min(100, progress));
  
  // Update video time based on progress
  const duration = videoRef.value.duration;
  if (isFinite(duration) && duration > 0) {
    const newTime = (progress / 100) * duration;
    
    // Ensure newTime is a valid, finite number and within the video duration
    if (isFinite(newTime) && newTime >= 0 && newTime <= duration) {
      videoRef.value.currentTime = newTime;
    }
    data.currentTime = video.currentTime;
    data.progress = (video.currentTime / video.duration) * 100;
    data.isPlaying = !video.paused;
  }
};
// function updateProgress() {
//   const video = videoRef.value;
//   data.currentTime = video.currentTime;
//   data.progress = (video.currentTime / video.duration) * 100;

//   // Update playing state
//   data.isPlaying = !video.paused;
// }




const updateVideoTime = (event) => {
  if (sliderRef.value && videoRef.value) {
    const slider = sliderRef.value;
    const rect = slider.getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    const percentage = (offsetX / rect.width) * 100;
    const clampedPercentage = Math.max(0, Math.min(100, percentage));

    videoRef.value.currentTime =
      (clampedPercentage / 100) * videoRef.value.duration;

    data.progress = clampedPercentage;
  }
};

// Hàm cập nhật thời gian cho box
const updateBoxTime = (index, startTime, endTime) => {
  selectionBoxes.value[index].startTime = startTime;
  selectionBoxes.value[index].endTime = endTime;
};

// Hàm bắt thời gian hiện tại của video
const captureTime = (regionIndex, timeType) => {
  if (videoRef.value) {
    const currentTime = videoRef.value.currentTime;
    selectionBoxes.value[regionIndex][timeType] = currentTime;
  }
};

// Hàm kiểm tra và hiển thị rectangle theo thời gian
const updateVideoTimeDisplay = () => {
  if (!videoRef.value) return;

  const currentTime = videoRef.value.currentTime;

  // Cập nhật trạng thái active cho các box
  selectionBoxes.value.forEach((box) => {
    box.active = currentTime >= box.startTime && currentTime <= box.endTime;
  });
};
</script>
