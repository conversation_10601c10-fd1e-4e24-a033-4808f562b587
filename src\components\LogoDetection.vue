<template>
  <div class="min-h-screen bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto">
      <!-- Loading Spinner -->
      <div v-if="loading" class="fixed inset-0 flex items-center justify-center bg-gray-700 bg-opacity-50 z-50">
        <div class="animate-spin border-t-4 border-blue-500 border-solid w-16 h-16 rounded-full"></div>
      </div>
      <!-- Video Upload Zone -->
      <div 
        @dragover.prevent
        @drop.prevent
        class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors"
      >
        <div v-if="!videoUrl" class="space-y-4 p-8">
          <div class="text-gray-500">
            Kéo thả video vào đây hoặc
            <label class="text-blue-500 hover:text-blue-600 cursor-pointer" @click="handleFileSelect">
              chọn file
            </label>
          </div>
        </div>
        
        <!-- Video Container -->
        <div v-else class="relative inline-block">
        <!-- Video Element -->
          <video
            ref="videoRef"
            :src="`http://localhost:3210/video`"
            class="max-w-full"
            @loadedmetadata="handleVideoLoad"
            @timeupdate="updateProgress"
            crossorigin="anonymous"
          ></video>
          





          <!-- Selection Overlay -->
<div 
  v-if="showSelectionBox"
  ref="overlayRef"
  class="absolute top-0 left-0 w-full h-full cursor-crosshair"
  @mousedown="startDrawing"
  @mousemove="draw"
  @mouseup="endDrawing"
  @mousemove.prevent="isDraggingBox ? moveBox($event) : (isResizing ? resizeBox($event) : null)"
  @mouseup.prevent="isDraggingBox ? endBoxMove() : (isResizing ? endResize() : null)"
>

            <!-- Current Selection -->
            <div
              v-if="currentBox"
              class="absolute border-2 border-blue-500 bg-blue-500 bg-opacity-20"
              :style="{
                left: `${currentBox.x}px`,
                top: `${currentBox.y}px`,
                width: `${currentBox.width}px`,
                height: `${currentBox.height}px`
              }"
            ></div>

  <!-- Existing Selection Boxes -->
  <div
    v-for="(box, index) in selectionBoxes"
    :key="index"
    class="absolute border-2 border-red-500 bg-red-500 bg-opacity-20"
    :class="{ 'hidden': box.active }"
    :style="{
      left: `${box.x}px`,
      top: `${box.y}px`,
      width: `${box.width}px`,
      height: `${box.height}px`
    }"
  >
    <!-- Di chuyển toàn bộ rectangle -->
    <div 
      @mousedown.prevent="startBoxMove(index, $event)"
      class="absolute inset-0 cursor-move"
    ></div>
    
    <!-- Các điểm resize -->
    <div 
      @mousedown.prevent="startResize(index, 'top-left', $event)" 
      class="absolute top-0 left-0 w-2 h-2 bg-orange-500 cursor-nwse-resize rounded-br-full"
    ></div>
    <div 
      @mousedown.prevent="startResize(index, 'top-right', $event)" 
      class="absolute top-0 right-0 w-2 h-2 bg-orange-500 cursor-nesw-resize rounded-bl-full"
    ></div>
    <div 
      @mousedown.prevent="startResize(index, 'bottom-left', $event)" 
      class="absolute bottom-0 left-0 w-2 h-2 bg-orange-500 cursor-nesw-resize rounded-tr-full"
    ></div>
    <div 
      @mousedown.prevent="startResize(index, 'bottom-right', $event)" 
      class="absolute bottom-0 right-0 w-2 h-2 bg-orange-500 cursor-nwse-resize rounded-tl-full"
    ></div>

    <!-- Nút xóa -->
    <button 
      @click.stop="removeBox(index)"
      class="absolute -top-3 -right-3 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center"
    >
      ×
    </button>
    
    <!-- Thông tin box -->
    <div class="absolute -bottom-6 left-0 text-xs bg-black text-white px-2 py-1 rounded opacity-50">
      {{ Math.round(getVideoCoords(box).x) }},{{ Math.round(getVideoCoords(box).y) }} 
      {{ Math.round(getVideoCoords(box).width) }}x{{ Math.round(getVideoCoords(box).height) }}
    </div>
  </div>
</div>
        </div>
      </div>
          <!-- Custom Timeline Slider -->
          <div v-if="videoUrl"  class="mt-2 w-full">
            <div 
              ref="sliderRef"
              class="relative w-full h-5 bg-gray-200 rounded cursor-pointer" 
              @click="seekVideo"
              @mousedown="startDragging"
              @mousemove="onDragging"
              @mouseup="stopDragging"
              @mouseleave="stopDragging"
            >
              <div 
                class="absolute left-0 top-0 h-full bg-blue-500 rounded" 
                :style="{ width: `${data.progress}%` }"
              ></div>
            </div>
            
            <!-- Time Display -->
            <div class="flex justify-between text-sm text-gray-600 mt-1">
              <span>{{ formatTime(data.currentTime) }}</span>
              <span>{{ formatTime(data.duration) }}</span>
            </div>
          </div>
      <!-- Controls -->
      <div v-if="videoUrl" class="mt-6 space-y-4">
        <!-- Progress Bar -->
        <div v-if="processing" class="w-full bg-gray-200 rounded-full h-2.5">
          <div
            class="bg-blue-600 h-2.5 rounded-full"
            :style="{ width: `${progress}%` }"
          ></div>
          <div class="text-xs text-gray-500 ml-2">{{progress}}%</div>
        </div>
        <div class="flex gap-4">
          <button
            @click="toggleSelectionBox"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {{ showSelectionBox ? 'Xong chọn vùng' : 'Chọn vùng' }}
          </button>
          

          <!-- Control Panel -->
          <div class="flex items-center justify-center space-x-4 border-2 border-gray-300 rounded-lg p-1">
            <button 
              @click="togglePlay" 
              class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
            >
              {{ data.isPlaying ? 'Pause' : 'Play' }}
            </button>
            <button 
              @click="toggleMute" 
              class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              {{ data.isMuted ? 'Unmute' : 'Mute' }}
            </button>
          </div>
          <button
            @click="processVideo"
            :disabled="processing || selectionBoxes.length === 0"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {{ processing ? 'Đang xử lý...' : 'Xử lý video' }}
          </button>
          <button
            @click="resetVideo"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            Reset
          </button>
        </div>
        


        <!-- Selection Info -->
        <!-- Thêm điều khiển thời gian cho mỗi rectangle -->
        <div v-if="selectionBoxes.length > 0" class="mt-4">
          <div v-for="(box, index) in selectionBoxes" :key="index" class="flex items-center gap-4 mb-2">
            <span>Vùng {{ index + 1 }}: </span>
            <label class="flex items-center">
              Start Time:
              <input 
                type="text" 
                :value="box.startTime" 
                @change="updateBoxTime(index, box.startTime, box.endTime)"
                class="ml-2 w-20 border rounded px-2 py-1"
                step="0.1"
                min="0"
              />
            <button 
              @click="captureTime(index, 'startTime')"
              class="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
            >
              Start
            </button>
            </label>
            <label class="flex items-center">
              End Time:
              <input 
                type="text" 
                :value="box.endTime" 
                @change="updateBoxTime(index, box.startTime, box.endTime)"
                class="ml-2 w-20 border rounded px-2 py-1"
                step="0.1"
                :min="box.startTime"
              />
            <button 
              @click="captureTime(index, 'endTime')"
              class="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
            >
              End
            </button>
            </label>
          </div>
        </div>
        <div v-if="selectionBoxes.length > 0" class="mt-4">
          <h3 class="font-semibold mb-2">Vùng đã chọn:</h3>
          <div class="space-y-2">
            <div v-for="(box, index) in selectionBoxes" :key="index" class="flex items-center gap-4">
              <span>Vùng {{ index + 1 }}: </span>
              <span>X: {{ Math.round(getVideoCoords(box).x) }}, </span>
              <span>Y: {{ Math.round(getVideoCoords(box).y) }}, </span>
              <span>W: {{ Math.round(getVideoCoords(box).width) }}, </span>
              <span>H: {{ Math.round(getVideoCoords(box).height) }}</span>
              <button 
                @click="removeBox(index)"
                class="text-red-500 hover:text-red-600"
              >
                Xóa
              </button>
            </div>
          </div>
        </div>
        <div v-if="savePath" class="mt-4">
           Filename: {{savePath}}
        </div>
        <LogViewer />
      </div>
    </div>
  </div>
</template>

<script setup>
import LogViewer from '@/components/LogViewer.vue'
import { VideoProcessor } from '@/services/videoProcession.ts'
import * as cv from "@techstark/opencv-js";

const { ipcRenderer } = require('electron');


window.cv = cv;
// State
const videoUrl = ref(null)
const videoRef = ref(null)
const overlayRef = ref(null)
const showSelectionBox = ref(false)
const isDrawing = ref(false)
const initBoxes = ref([])
//- const selectionBoxes = ref([])
const selectionBoxes = ref([
  {
    x: 0, 
    y: 0, 
    width: 100, 
    height: 100,
    startTime: 0,   // Thời điểm bắt đầu xuất hiện
    endTime: 10,    // Thời điểm kết thúc
    active: true    // Trạng thái hiển thị
  }
])
const currentBox = ref(null)
const processing = ref(false)
const progress = ref(0)
const savePath = ref('')
const videoPath = ref('')
const sliderRef = ref(null)
const isDragging = ref(false)
// Video dimensions
const videoWidth = ref(0)
const videoHeight = ref(0)
const loading = ref(false)

const data = reactive({
  progress: 0,
  currentTime: 0,
  duration: 0,
  isPlaying: false,
  isMuted: false,
})


let unlistenDr = null
let unlistenLog = null
// Initialize

// Define emit for TypeScript
const emit = defineEmits()





onMounted(async () => {
  try {
    emit('set-title', 'Video Logo Remover v2')
    document.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.stopPropagation();
    });

    document.addEventListener('drop', (event) => {
        event.preventDefault();
        event.stopPropagation();
        handleFile(event.dataTransfer.files[0].path);
    });

    ipcRenderer.on('ffmpeg-progress', (e, event) => {
      const progressValue = event;

      if (progressValue === 100) {
        if (progress.value < 99) {
          console.warn('Unexpected completion detected.');
        }
        processing.value = false;
        progress.value = 100;
        S.toast.success('Video processed successfully!');
      } else if (progressValue === -1) {
        // Xử lý lỗi
        processing.value = false;
        progress.value = 0;
        S.toast.error('An error occurred during processing!');
      } else {
        // Cập nhật tiến trình
        progress.value = progressValue;
      }
    });
  if (videoRef.value) {
    videoRef.value.addEventListener('timeupdate', updateVideoTimeDisplay)
  }
    console.log('_loaded')
  } catch (error) {
    console.error('Failed to load:', error)
  }
})

onUnmounted(() => {
  //- if (unlistenLog) {
  //-   unlistenLog()
  //- }
    if (videoRef.value) {
    videoRef.value.removeEventListener('timeupdate', updateVideoTimeDisplay)
  }
  //- if (unlistenDr) {
  //-   unlistenDr()
  //- }
  if (prevFrame) {
    prevFrame.delete();
  }


})



const resetVideo =() => {
  videoUrl.value = null
  videoRef.value = null
  overlayRef.value = null
  showSelectionBox.value = false
  isDrawing.value = false
  selectionBoxes.value = []
  currentBox.value = null
  processing.value = false
  progress.value = 0
  savePath.value = ''
  videoPath.value = ''
  data.progress = 0
  detections.value = []
  initBoxes.value = []
  if (prevFrame) {
    prevFrame.delete();
  }
}

const playAudio =() => {
  if (videoRef.value) {
    videoRef.value.play()
  }
  else {
    console.log('Video not loaded')
  }
  console.log([videoRef.value])
}







const handleDrop = (event) => {
  const file = event.dataTransfer.files[0]
  console.log('File', file)
  if (file && file.type.startsWith('video/')) {
    handleFile(file)
  }
}

const handleFileSelect = async (event) => {
  const filePaths = await ipcRenderer.invoke('dialog:openFile');
  console.log(filePaths);
  handleFile(filePaths)
}

const handleFile = async (selected) => {
  console.log('selected',selected)
  loading.value = true;
    if(selected){
      const randomString = Math.random().toString(36).substring(2, 8); // Tạo chuỗi ngẫu nhiên
      const pathParts = selected.split(/[/\\]/); // Tách các phần từ đường dẫn (Windows hoặc Unix)
      const fullFileName = pathParts.pop(); // Lấy tên file gốc
      const fileDirectory = pathParts.join('/'); // Lấy thư mục

      const [fileName, extension] = fullFileName.split(/\.(?=[^\.]+$)/); // Tách tên và phần mở rộng
      const newFileName = `${fileName}-delogo-${randomString}.${extension}`; // Gắn tên mới
      savePath.value = `${fileDirectory}/${newFileName}`; // Tạo đường dẫn mới
      videoPath.value = selected
      console.log("Original file path:", selected);
      console.log("New file path:", savePath.value);
      const videoData = await ipcRenderer.invoke("set_path_video", selected);
      //- const tempFilePath = await invoke('create_temp_file', { path: selected });

      // Tạo Blob từ file tạm (có thể sử dụng fetch hoặc File API nếu cần)
      //- const response = await fetch(`file://${tempFilePath}`);
      //- const videoData = await response.arrayBuffer();
      //- const response = await fetch(`file://${selected}`)
      //- const videoData = await response.arrayBuffer()
      //- const blob = new Blob([new Uint8Array(videoData)], { type: "video/mp4" });
      //- console.log(URL.createObjectURL(blob), selected)
      videoUrl.value = selected//URL.createObjectURL(blob);
      selectionBoxes.value = []
    }
    loading.value = false;
}

const handleVideoLoad = () => {
  const video = videoRef.value
  videoWidth.value = video.videoWidth
  videoHeight.value = video.videoHeight
  data.duration = video.duration
}

const toggleSelectionBox = () => {
  showSelectionBox.value = !showSelectionBox.value
}

// Get relative coordinates within the video element
const getRelativeCoordinates = (event) => {
  const rect = overlayRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  return { x, y }
}

// Convert display coordinates to video coordinates
const getVideoCoords = (displayBox) => {
  if (!videoRef.value) return { x: 0, y: 0, width: 0, height: 0 }
  
  const videoElement = videoRef.value
  const scaleX = videoWidth.value / videoElement.offsetWidth
  const scaleY = videoHeight.value / videoElement.offsetHeight
  
  return {
    x: displayBox.x * scaleX,
    y: displayBox.y * scaleY,
    width: displayBox.width * scaleX,
    height: displayBox.height * scaleY
  }
}


const startDrawing = (event) => {
  const { x, y } = getRelativeCoordinates(event)
  currentBox.value = { x, y, width: 0, height: 0 }
  isDrawing.value = true
}

//- const draw = (event) => {
//-   if (!isDrawing.value || !currentBox.value) return
  
//-   const { x, y } = getRelativeCoordinates(event)
  
//-   currentBox.value = {
//-     ...currentBox.value,
//-     width: x - currentBox.value.x,
//-     height: y - currentBox.value.y
//-   }
//- }

const draw = (event) => {
  if (!isDrawing.value || !currentBox.value) return
  
  const { x, y } = getRelativeCoordinates(event)
  const videoElement = videoRef.value
  
  // Tính toán kích thước tối đa cho phép
  const maxWidth = videoElement.offsetWidth - currentBox.value.x - 1
  const maxHeight = videoElement.offsetHeight - currentBox.value.y - 1

  // Tính toán width và height mới
  let newWidth = x - currentBox.value.x
  let newHeight = y - currentBox.value.y

  // Giới hạn kích thước để không vượt quá video
  newWidth = Math.min(Math.max(newWidth, 1), maxWidth)
  newHeight = Math.min(Math.max(newHeight, 1), maxHeight)
  
  currentBox.value = {
    ...currentBox.value,
    width: newWidth,
    height: newHeight
  }
}






const endDrawing = () => {
  if (currentBox.value && (currentBox.value.width || currentBox.value.height)) {
    // Normalize negative dimensions
    let { x, y, width, height } = currentBox.value
    
    if (width < 0) {
      x += width
      width = Math.abs(width)
    }
    
    if (height < 0) {
      y += height
      height = Math.abs(height)
    }
    
    if (width > 0 && height > 0) {
      x = Math.max(1, x)
      y = Math.max(1, y)
      selectionBoxes.value.push({ x, y, width, height })
      initBoxes.value = [{ x, y, width, height }]
      S.initBoxes = initBoxes.value
    }

  }
  
  currentBox.value = null
  isDrawing.value = false
}

const removeBox = (index) => {
  selectionBoxes.value.splice(index, 1)
}


function generateFilterComplex(data) {
  const filters = [];
  const outputs = [];

  data.forEach((item, index) => {
    if (index < data.length - 1) {
      const startTime = item.time;
      const endTime = data[index + 1].time;
      const rect = item.rect;

      filters.push(
        `[0:v]trim=start=${startTime}:end=${endTime},setpts=PTS-STARTPTS,delogo=x=${Math.round(rect.x)}:y=${Math.round(rect.y)}:w=${Math.round(rect.width)}:h=${Math.round(rect.height)}[v${index}]`
      );
      outputs.push(`[v${index}]`);
    }
  });

  // Tạo filter concat
  const concatFilter = `${filters.join(";")};${outputs.join("")}concat=n=${outputs.length}:v=1:a=0[outv]`;
  return concatFilter;
}


//- const processVideo = async () => {
//-   if (!videoUrl.value || selectionBoxes.value.length === 0) return
  
//-   processing.value = true
//-   progress.value = 0
//-   try {
//-     // Convert selection boxes to video coordinates and create filter string
//-     const filterss = selectionBoxes.value.map(box => {
//-       const videoCoords = getVideoCoords(box)
//-       if (
//-         videoCoords.x < 0 || 
//-         videoCoords.y < 0 || 
//-         videoCoords.x + videoCoords.width > videoWidth.value || 
//-         videoCoords.y + videoCoords.height > videoHeight.value
//-       ) {
//-         console.error("Invalid delogo filter:", videoCoords)
//-         return null
//-       }
//-       return `delogo=x=${Math.round(videoCoords.x)}:y=${Math.round(videoCoords.y)}:w=${Math.round(videoCoords.width)}:h=${Math.round(videoCoords.height)}`
//-     }).filter(filter => filter !== null).join(',')
//-     const filters = generateFilterComplex(detections.value);

//-     const videoInfo = {
//-       width: videoWidth.value,
//-       height: videoHeight.value,
//-       duration: data.duration
//-     }

//-       await VideoProcessor.processVideo({
//-         input: videoPath.value,
//-         output: savePath.value,
//-         filters,
//-         videoInfo,
//-         detected: 'yes'
//-       })
   
//-   } catch (error) {
//-     console.error('Error processing video:', error)
//-     alert(`Có lỗi xảy ra khi xử lý video: ${error.toString()}`)
//-     processing.value = false
//-   } finally {
//-     //- processing.value = false
//-     //- progress.value = 100
//-   }
//- }

const processVideo = async () => {
  if (!videoUrl.value || selectionBoxes.value.length === 0) return
  
  processing.value = true
  progress.value = 0
  try {
    // Tạo segments cho video dựa trên thời gian bắt đầu/kết thúc của logo
    const segments = [];
    
    // Segment trước logo (giữ nguyên)
    if (detections.value[0]?.time > 0) {
      segments.push({
        time: 0,
        rect: null // Không áp dụng delogo
      });
    }

    // Segments có logo
    detections.value.forEach((detection, index) => {
      segments.push({
        time: detection.time,
        rect: detection.rect
      });
    });

    // Segment cuối cùng (đến hết video)
    const lastTime = videoRef.value.duration;
    if (segments[segments.length - 1]?.time < lastTime) {
      segments.push({
        time: lastTime,
        rect: null
      });
    }

    // Tạo filter complex string
    const filters = segments.map((segment, index) => {
      if (!segment.rect) {
        // Đoạn không có logo - giữ nguyên video gốc
        return `[0:v]trim=start=${segment.time}:end=${segments[index + 1]?.time || lastTime},setpts=PTS-STARTPTS[v${index}]`;
      } else {
        // Đoạn có logo - áp dụng delogo
        return `[0:v]trim=start=${segment.time}:end=${segments[index + 1]?.time || lastTime},setpts=PTS-STARTPTS,delogo=x=${Math.round(segment.rect.x)}:y=${Math.round(segment.rect.y)}:w=${Math.round(segment.rect.width)}:h=${Math.round(segment.rect.height)}[v${index}]`;
      }
    }).join(';');

    // Thêm lệnh concat
    const outputs = segments.map((_, i) => `[v${i}]`).join('');
    const filterComplex = `${filters};${outputs}concat=n=${segments.length}:v=1:a=0[outv]`;
    console.log('Filter complex:', filterComplex);
    const videoInfo = {
      width: videoWidth.value,
      height: videoHeight.value,
      duration: data.duration
    }

    await VideoProcessor.processVideo({
      input: videoPath.value,
      output: savePath.value,
      filters: filterComplex,
      videoInfo,
      detected: 'yes'
    })
   
  } catch (error) {
    console.error('Error processing video:', error)
    S.toast.error(`Có lỗi xảy ra khi xử lý video: ${error.toString()}`)
    processing.value = false
  }
}



const formatTime = (seconds) =>{
  // Convert seconds to MM:SS format
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const seekVideo = (event) => {
  if (sliderRef.value && videoRef.value) {
    const slider = sliderRef.value
    const rect = slider.getBoundingClientRect()
    const offsetX = event.clientX - rect.left
    const percentage = (offsetX / rect.width) * 100
    
    // Đảm bảo percentage nằm trong khoảng 0-100
    const clampedPercentage = Math.max(0, Math.min(100, percentage))
    
    videoRef.value.currentTime = (clampedPercentage / 100) * videoRef.value.duration
  }
}
function updateProgress() {
  const video = videoRef.value
  data.currentTime = video.currentTime
  data.progress = (video.currentTime / video.duration) * 100
  
  // Update playing state
  data.isPlaying = !video.paused
}

function togglePlay() {
  const video = videoRef.value
  detectLogoInVideo()
  if (video.paused) {
    video.play()
    data.isPlaying = true
  } else {
    video.pause()
    data.isPlaying = false
  }
}

function toggleMute() {
  const video = videoRef.value
  video.muted = !video.muted
  data.isMuted = video.muted
}

const startDragging = (event) => {
  isDragging.value = true
  updateVideoTime(event)
}

const onDragging = (event) => {
  if (isDragging.value) {
    updateVideoTime(event)
  }
}

const stopDragging = () => {
  isDragging.value = false
}

const updateVideoTime = (event) => {
  if (sliderRef.value && videoRef.value) {
    const slider = sliderRef.value
    const rect = slider.getBoundingClientRect()
    const offsetX = event.clientX - rect.left
    const percentage = (offsetX / rect.width) * 100
    const clampedPercentage = Math.max(0, Math.min(100, percentage))
    
    videoRef.value.currentTime = (clampedPercentage / 100) * videoRef.value.duration
    
    data.progress = clampedPercentage
  }
}

const detections = ref([]);
S.detections = detections.value

let prevFrame = null;

function detectLogoInVideo() {
  const videoElement = videoRef.value;
  const initialBox = initBoxes.value[0];

  if (!initialBox || !window.cv) {
    console.error('OpenCV hoặc initialBox không khả dụng');
    return;
  }

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  let prevDetectedBox = { ...initialBox };

  let consecutiveFailures = 0;
  
  const MATCH_THRESHOLD = 0.7;
  const MAX_SEARCH_PADDING = 80;
  const MIN_SEARCH_PADDING = 30;

  const isValidPosition = (rect, videoWidth, videoHeight) => {
    // Đảm bảo khoảng cách tối thiểu là 1px từ mọi cạnh
    return (
      rect.x >= 1 && // Thay vì >= 0
      rect.y >= 1 && // Thay vì >= 0
      rect.width > 0 &&
      rect.height > 0 &&
      rect.x + rect.width <= videoWidth - 1 && // Thêm -1 để cách viền
      rect.y + rect.height <= videoHeight - 1   // Thêm -1 để cách viền
    );
  };

  videoElement.addEventListener('play', () => {
    const processFrame = () => {
      try {
        if (videoElement.paused || videoElement.ended) return;

        if (videoElement.videoWidth === 0 || videoElement.videoHeight === 0) {
          requestAnimationFrame(processFrame);
          return;
        }

        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        ctx.drawImage(videoElement, 0, 0);

        const currentMat = cv.imread(canvas);
        
        if (prevFrame) {
          const searchPadding = Math.max(
            MAX_SEARCH_PADDING - consecutiveFailures * 5, 
            MIN_SEARCH_PADDING
          );

          const searchRegion = new cv.Rect(
            Math.max(0, prevDetectedBox.x - searchPadding),
            Math.max(0, prevDetectedBox.y - searchPadding),
            Math.min(
              prevDetectedBox.width + searchPadding * 2,
              currentMat.cols - Math.max(0, prevDetectedBox.x - searchPadding)
            ),
            Math.min(
              prevDetectedBox.height + searchPadding * 2,
              currentMat.rows - Math.max(0, prevDetectedBox.y - searchPadding)
            )
          );

          if (searchRegion.width <= 0 || searchRegion.height <= 0) {
            currentMat.delete();
            requestAnimationFrame(processFrame);
            return;
          }

          const searchMat = currentMat.roi(searchRegion);

          const templateRegion = new cv.Rect(
            prevDetectedBox.x,
            prevDetectedBox.y,
            prevDetectedBox.width,
            prevDetectedBox.height
          );

          if (!isValidPosition(templateRegion, prevFrame.cols, prevFrame.rows)) {
            searchMat.delete();
            currentMat.delete();
            requestAnimationFrame(processFrame);
            return;
          }

          const logoTemplate = prevFrame.roi(templateRegion);
          const result = new cv.Mat();
          
          cv.matchTemplate(searchMat, logoTemplate, result, cv.TM_CCOEFF_NORMED);
          const minMax = cv.minMaxLoc(result);

          if (minMax.maxVal > MATCH_THRESHOLD) {
            let detectedLocation = {
              x: searchRegion.x + minMax.maxLoc.x,
              y: searchRegion.y + minMax.maxLoc.y,
              width: initialBox.width,
              height: initialBox.height
            };

            detectedLocation = {
              x: Math.max(1, Math.min(detectedLocation.x, videoElement.videoWidth - detectedLocation.width - 1)),
              y: Math.max(1, Math.min(detectedLocation.y, videoElement.videoHeight - detectedLocation.height - 1)),
              width: Math.min(detectedLocation.width, videoElement.videoWidth - detectedLocation.x - 1),
              height: Math.min(detectedLocation.height, videoElement.videoHeight - detectedLocation.y - 1)
            };

            if (isValidPosition(detectedLocation, videoElement.videoWidth, videoElement.videoHeight)) {
              detections.value.push({
                time: videoElement.currentTime,
                rect: detectedLocation
              });
              
              selectionBoxes.value = [{
                ...detectedLocation,
                startTime: 0,
                endTime: videoElement.duration
              }];

              prevDetectedBox = detectedLocation;
              consecutiveFailures = 0;
            } else {
              consecutiveFailures++;
            }
          } else {
            consecutiveFailures++;
            if (consecutiveFailures > 10) {
              if (isValidPosition(initialBox, videoElement.videoWidth, videoElement.videoHeight)) {
                prevDetectedBox = { ...initialBox };
              }
              consecutiveFailures = 0;
            }
          }

          searchMat.delete();
          logoTemplate.delete();
          result.delete();
        }

        if (prevFrame) prevFrame.delete();
        prevFrame = currentMat;

        requestAnimationFrame(processFrame);
      } catch (error) {
        console.error('Lỗi xử lý frame:', error);
        requestAnimationFrame(processFrame);
      }
    };

    processFrame();
  });

}



// 

// Thêm state để quản lý rectangle đang được chọn để di chuyển
const selectedBoxIndex = ref(null)
const isDraggingBox = ref(false)

const isResizing = ref(false)
const resizeDirection = ref(null)

// Hàm thêm thời gian cho rectangle
const addTimeToBox = (index, startTime, endTime) => {
  selectionBoxes.value[index].startTime = startTime
  selectionBoxes.value[index].endTime = endTime
}



// Hàm bắt đầu di chuyển rectangle
const startBoxMove = (index, event) => {
  selectedBoxIndex.value = index
  isDraggingBox.value = true
  event.stopPropagation() // Ngăn chặn sự kiện vẽ rectangle mới
}

// Hàm di chuyển rectangle
//- const moveBox = (event) => {
//-   if (!isDraggingBox.value || selectedBoxIndex.value === null) return
  
//-   const { x, y } = getRelativeCoordinates(event)
  
//-   selectionBoxes.value[selectedBoxIndex.value] = {
//-     ...selectionBoxes.value[selectedBoxIndex.value],
//-     x: Math.max(0, x - selectionBoxes.value[selectedBoxIndex.value].width / 2),
//-     y: Math.max(0, y - selectionBoxes.value[selectedBoxIndex.value].height / 2)
//-   }

//-   initBoxes.value = [selectionBoxes.value[selectedBoxIndex.value]]
//- }
const moveBox = (event) => {
  if (!isDraggingBox.value || selectedBoxIndex.value === null) return
  
  const { x, y } = getRelativeCoordinates(event)
  const videoElement = videoRef.value
  const currentBox = selectionBoxes.value[selectedBoxIndex.value]
  
  // Tính toán vị trí mới, đảm bảo cách viền 1px
  const newX = Math.max(1, Math.min(
    x - currentBox.width / 2,
    videoElement.offsetWidth - currentBox.width - 1
  ))
  
  const newY = Math.max(1, Math.min(
    y - currentBox.height / 2,
    videoElement.offsetHeight - currentBox.height - 1
  ))
  
  selectionBoxes.value[selectedBoxIndex.value] = {
    ...currentBox,
    x: newX,
    y: newY
  }

  initBoxes.value = [selectionBoxes.value[selectedBoxIndex.value]]
}



// Hàm kết thúc di chuyển
const endBoxMove = () => {
  isDraggingBox.value = false
  selectedBoxIndex.value = null
}

// Hàm bắt đầu resize
const startResize = (index, direction, event) => {
  selectedBoxIndex.value = index
  isResizing.value = true
  resizeDirection.value = direction
  event.stopPropagation()
}

// Hàm resize rectangle
//- const resizeBox = (event) => {
//-   if (!isResizing.value || selectedBoxIndex.value === null) return
  
//-   const { x, y } = getRelativeCoordinates(event)
//-   const box = selectionBoxes.value[selectedBoxIndex.value]
  
//-   switch(resizeDirection.value) {
//-     case 'top-left':
//-       box.width = box.x + box.width - x
//-       box.height = box.y + box.height - y
//-       box.x = x
//-       box.y = y
//-       break
//-     case 'top-right':
//-       box.width = x - box.x
//-       box.height = box.y + box.height - y
//-       box.y = y
//-       break
//-     case 'bottom-left':
//-       box.width = box.x + box.width - x
//-       box.height = y - box.y
//-       box.x = x
//-       break
//-     case 'bottom-right':
//-       box.width = x - box.x
//-       box.height = y - box.y
//-       break
//-   }
//- }

const resizeBox = (event) => {
  if (!isResizing.value || selectedBoxIndex.value === null) return
  
  const { x, y } = getRelativeCoordinates(event)
  const box = selectionBoxes.value[selectedBoxIndex.value]
  const videoElement = videoRef.value
  
  // Tính toán giới hạn cho resize
  const minSize = 1
  const maxX = videoElement.offsetWidth - 1
  const maxY = videoElement.offsetHeight - 1
  
  let newX = box.x
  let newY = box.y
  let newWidth = box.width
  let newHeight = box.height

  switch(resizeDirection.value) {
    case 'top-left':
      newX = Math.max(1, Math.min(x, box.x + box.width - minSize))
      newY = Math.max(1, Math.min(y, box.y + box.height - minSize))
      newWidth = box.x + box.width - newX
      newHeight = box.y + box.height - newY
      break
    case 'top-right':
      newWidth = Math.max(minSize, Math.min(x - box.x, maxX - box.x))
      newY = Math.max(1, Math.min(y, box.y + box.height - minSize))
      newHeight = box.y + box.height - newY
      break
    case 'bottom-left':
      newX = Math.max(1, Math.min(x, box.x + box.width - minSize))
      newWidth = box.x + box.width - newX
      newHeight = Math.max(minSize, Math.min(y - box.y, maxY - box.y))
      break
    case 'bottom-right':
      newWidth = Math.max(minSize, Math.min(x - box.x, maxX - box.x))
      newHeight = Math.max(minSize, Math.min(y - box.y, maxY - box.y))
      break
  }

  selectionBoxes.value[selectedBoxIndex.value] = {
    ...box,
    x: newX,
    y: newY,
    width: newWidth,
    height: newHeight
  }
}



// Hàm kết thúc resize
const endResize = () => {
  isResizing.value = false
  selectedBoxIndex.value = null
}

// Hàm thêm thời gian cho rectangle
const updateBoxTime = (index, startTime, endTime) => {
  selectionBoxes.value[index].startTime = startTime
  selectionBoxes.value[index].endTime = endTime
}

// Hàm kiểm tra và hiển thị rectangle theo thời gian
const updateVideoTimeDisplay = () => {
  if (!videoRef.value) return

  const currentTime = videoRef.value.currentTime
  
  // Cập nhật trạng thái active cho các box
  selectionBoxes.value.forEach(box => {
    box.active = currentTime >= box.startTime && currentTime <= box.endTime
  })
}
S.updateVideoTimeDisplay = updateVideoTimeDisplay
S.selectionBoxes = selectionBoxes
//- const captureTime = (regionIndex, timeType) => {
//-   if (videoRef.value) {
//-     const currentTime = videoRef.value.currentTime
//-     selectionBoxes.value[regionIndex][timeType] = currentTime
//-   }
//- }
const captureTime = (regionIndex, timeType) => {
  if (videoRef.value) {
    const currentTime = videoRef.value.currentTime;
    
    if (timeType === 'endTime') {
      // Thêm detection cuối cùng với thời gian kết thúc được chọn
      detections.value.push({
        time: currentTime,
        rect: null // Đánh dấu kết thúc vùng logo
      });
    } else {
      // Xử lý startTime như bình thường
      selectionBoxes.value[regionIndex][timeType] = currentTime;
    }
  }
}



// Helper function to format time as HH:MM:SS
//- const formatTime = (seconds) => {
//-   const h = Math.floor(seconds / 3600)
//-   const m = Math.floor((seconds % 3600) / 60)
//-   const s = Math.floor(seconds % 60)
//-   return [h, m, s]
//-     .map(v => v.toString().padStart(2, '0'))
//-     .join(':')
//- }



</script>