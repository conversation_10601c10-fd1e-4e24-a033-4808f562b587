
<!-- src/App.vue -->
<template>
  <div class="min-h-screen bg-gray-100">
    <nav class="bg-white shadow-md">
      <div class="container mx-auto flex justify-between items-center p-4">
        <router-link to="/" class="text-2xl font-bold text-blue-600">
          {{ pageTitle }}
        </router-link>
        
        <div class="space-x-4">
          <router-link 
            to="/delogo" 
            class="text-gray-700 hover:text-blue-600 transition"
          >
            Delogo
          </router-link>
          <router-link 
            to="/delogov2" 
            class="text-gray-700 hover:text-blue-600 transition"
          >
            Delogo V3
          </router-link>
          <router-link 
            to="/detect" 
            class="text-gray-700 hover:text-blue-600 transition"
          >
            Delogo V2
          </router-link>
          <router-link 
            to="/media" 
            class="text-gray-700 hover:text-blue-600 transition"
          >
            Media
          </router-link>

        </div>
      </div>
    </nav>

    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" @set-title="updatePageTitle" />
      </transition>
    </router-view>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import { usePageTitleStore } from '@/stores/pageTitle'
import { useRoute } from 'vue-router'

const pageTitleStore = usePageTitleStore()
const pageTitle = computed(() => pageTitleStore.title)
document.addEventListener("contextmenu", (event) => {
  event.preventDefault();
});
function updatePageTitle(title: string) {
  pageTitleStore.setTitle(title)
}
</script>
<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>