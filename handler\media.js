const path = require('path');
const fs = require('fs');
const ffmpeg = require('fluent-ffmpeg');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);

const outputDir = path.join(ROOT_DIR, 'Converted'); // You can modify this based on your app's needs

// Function to create the output directory
async function createOutputDir(type) {
  try {
    const outputDir = path.join(ROOT_DIR, 'Converted', type);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    return outputDir;
  } catch (err) {
    throw new Error('Failed to create output directory: ' + err.message);
  }
}

// Convert media function
const convertMedia =  async (event, {inputFiles, outputFormat, optionAgrs, type}) => {
  console.log({inputFiles, outputFormat, optionAgrs});
 const outputDir =  await createOutputDir(type);
  let statuses = [];

  for (const inputFile of inputFiles) {
    const fileName = path.basename(inputFile, path.extname(inputFile));
    let outputFile = path.join(outputDir, `${fileName}-converted.${outputFormat}`);
    if (outputFormat === 'jpg' || outputFormat === 'png') {
      outputFile = path.join(outputDir, `${fileName}-%03d-converted.${outputFormat}`);
    }

    try {
      await new Promise((resolve, reject) => {
        ffmpeg(inputFile)
          .output(outputFile)
          .outputOptions(optionAgrs)
          .on('end', () => {
            statuses.push({ file: inputFile, status: 'success' });
            resolve();
          })
          .on('error', (err) => {
            statuses.push({ file: inputFile, status: `failed: ${err.message}` });
            reject(err);
          })
          .run();
      });
    } catch (error) {
      statuses.push({ file: inputFile, status: 'failed to start' });
    }
  }

  return statuses.map(status => `File: ${status.file} - Status: ${status.status}`).join('\n');
}

// Open the output folder
const openOutputFolder = async (event, typeFolder) => {
  const folderPath = path.join(outputDir, typeFolder || '');
  try {
    // if windows
    if (process.platform === 'win32') {
      return await exec(`start "" "${folderPath}"`);
    }
    // macOS
    if (process.platform === 'darwin') {
      return await exec(`open "${folderPath}"`);
    }

    return await exec(`explorer ${folderPath}`); // Windows
  } catch(e) {
    console.log('e',e);
    // try {
    //   return await exec(`open ${folderPath}`); // macOS
    // } catch {
    //   return await exec(`xdg-open ${folderPath}`); // Linux
    // }
  }
}


module.exports = {
  convertMedia,
  openOutputFolder,
};