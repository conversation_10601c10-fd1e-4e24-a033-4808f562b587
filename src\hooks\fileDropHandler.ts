import { event } from '@tauri-apps/api'
import { computed, ref, onUnmounted } from 'vue'

export function useFileDrop() {
  const droppedFiles = ref<string[]>([])
  const unlistenFns: (() => void)[] = []

  const initFileDropHandler = async () => {
    try {
      // Lắng nghe sự kiện thả file
      const unlistenDrop = await event.listen('tauri://file-drop', (dropEvent: any) => {
        console.log('Files dropped:', dropEvent.payload)
        
        // Kiểm tra và lưu các file path
        if (Array.isArray(dropEvent.payload)) {
          droppedFiles.value = dropEvent.payload
        }
      })
      unlistenFns.push(unlistenDrop)

      // Lắng nghe sự kiện hover
      const unlistenHover = await event.listen('tauri://file-drop-hover', (hoverEvent: any) => {
        console.log('Files hovering:', hoverEvent)
      })
      unlistenFns.push(unlistenHover)

      // Lắ<PERSON> nghe sự kiện hủy thả file
      const unlistenCancel = await event.listen('tauri://file-drop-cancelled', (cancelEvent: any) => {
        console.log('File drop cancelled:', cancelEvent)
      })
      unlistenFns.push(unlistenCancel)

    } catch (error) {
      console.error('Error setting up file drop handler:', error)
    }
  }

  // Hàm dọn dẹp các event listener
  const cleanupFileDropHandlers = () => {
    unlistenFns.forEach(unlisten => unlisten())
    unlistenFns.length = 0
  }

  // Xử lý file
  const processDroppedFiles = () => {
    droppedFiles.value.forEach(file => {
      console.log('Processing file:', file)
      // Thêm logic xử lý file của bạn ở đây
    })
  }

  // Getter để kiểm tra có file được thả không
  const hasDroppedFiles = computed(() => droppedFiles.value.length > 0)

  // Đảm bảo cleanup khi component unmount
  onUnmounted(cleanupFileDropHandlers)

  return {
    droppedFiles,
    initFileDropHandler,
    processDroppedFiles,
    hasDroppedFiles
  }
}