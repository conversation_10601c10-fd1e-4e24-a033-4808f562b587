const express = require('express');
const path = require('path');
const http = require('http');

const fs = require('fs');


const server = express();
const port = 3210;


server.get('/video', (req, res) => {
    if(!S.filePath) return res.status(404).end();
    const file = path.join(S.filePath);
    if (!fs.existsSync(file))
        return res.status(404).send('File not found');


    res.sendFile(file);
});

http.createServer(server).listen(port, () => {
    console.log(`Server is running at http://localhost:${port}`);
});