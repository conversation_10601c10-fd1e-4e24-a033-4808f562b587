{"name": "delogo-ffmpeg", "private": true, "version": "1.0.5", "main": "main.js", "scripts": {"dev": "vite", "build": "vite build", "el:dev": "set \"NODE_ENV=development\" && concurrently \"yarn dev\" \"electron .\"", "build:watch": "vite build --mode development --watch", "electron:dev": "wait-on dist/index.html && electron .", "e:dev": "set \"NODE_ENV=development\" && concurrently \"yarn build:watch\" \"yarn electron:dev\"", "el:build": "yarn build && electron-builder", "preview": "vite preview"}, "dependencies": {"@techstark/opencv-js": "^4.10.0-release.1", "express": "^4.21.2", "fluent-ffmpeg": "2.1.2", "opencv.js": "^1.2.1", "pinia": "^2.2.8", "uuid": "^11.0.5", "vue": "^3.3.4", "vue-router": "^4.5.0", "vue3-toastify": "^0.2.8"}, "devDependencies": {"@types/node": "^22.10.1", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "crypto-browserify": "^3.12.1", "electron": "^21", "electron-builder": "^25.1.8", "electron-reload": "^2.0.0-alpha.1", "memfs": "^4.17.0", "path-browserify": "^1.0.1", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "^5.2.2", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^5.3.1", "vite-plugin-electron": "^0.29.0", "vue-tsc": "^2.0.22", "wait-on": "^8.0.2"}, "build": {"appId": "com.delogo.app", "productName": "Del<PERSON>", "files": ["dist/**/*", "handler/**/*", "main.js", "ipcHandlers.js"], "directories": {"buildResources": "build", "output": "release"}}}