const { ipc<PERSON><PERSON><PERSON> } = require('electron');

export interface ConversionOptions {
  type: 'audio' | 'video' | 'image' | 'videoImage'
  outputFormat: string
  inputFiles: string[],
  numberOfFrames: number
}

export class ConversionService {
  static async selectInputFiles(): Promise<string[]> {
    return await ipc<PERSON>enderer.invoke('dialog:openFiles') as string[]
  }

  static async convert(options: ConversionOptions): Promise<string> {
    // const outputDir = await appDataDir()
    const pathParts = options.inputFiles[0].split(/[/\\]/) 
    const fullFileName = pathParts.pop() as string
    let outputDir = pathParts.join('/'); 
    
    const [fileName, extension] = fullFileName.split(/\.(?=[^\.]+$)/); // Tách tên và phần mở rộng
    const newFileName = `${fileName}-converted.${extension}`; // Gắn tên mới
    console.log('Converting', fullFileName, outputDir, newFileName);
    console.log('options',options);
    const optionAgrs = ()=>{
        if(options.type === 'audio'){
           if(options.outputFormat == 'mp3') return ["-b:a", "128k", "-vn", "-acodec","libmp3lame"]
           return ["-vn", "-acodec","pcm_s16le", "-ac", "2", "-ar", "8000"]
        }
        if(options.type === 'video'){
            if(options.outputFormat == '1080p') {
                options.outputFormat = 'mp4';
                return ["-vf", "scale=1920:1080", "-crf", "18", "-preset", "fast",  "-c:a", "copy"]
            }
            if(options.outputFormat == '720p') {
                options.outputFormat = 'mp4';
                return ["-vf", "scale=1280:720", "-crf", "22", "-preset", "fast",  "-c:a", "copy"]
            }
            if(options.outputFormat == 'blurred') {
                options.outputFormat = 'mp4';
                // "[0:v]scale=ih*16/9:-1,boxblur=luma_radius=min(h\\,w)/20:luma_power=1:chroma_radius=min(cw\\,ch)/20:chroma_power=1[bg];[bg][0:v]overlay=(W-w)/2:(H-h)/2,crop=h=iw*9/16"
                //"[0:v]scale=ih*16/9:-1,gblur=sigma=10[bg];[bg][0:v]overlay=(W-w)/2:(H-h)/2,crop=h=iw*9/16"
                return ["-filter_complex", "[0:v]scale=ih*16/9:-1,boxblur=luma_radius=min(h\\,w)/20:luma_power=1:chroma_radius=min(cw\\,ch)/20:chroma_power=1[bg];[bg][0:v]overlay=(W-w)/2:(H-h)/2,crop=h=iw*9/16", "-crf", "23", "-preset", "fast"]
            }
            return ["-crf", "23", "-preset", "fast"]
        }
        if(options.type === 'image'){
            return []
        }
        if(options.type === 'videoImage'){
            options.outputFormat = 'jpg';
            outputDir = `${outputDir}/${fileName}-images`
            return ["-vf", `fps=1/${options.numberOfFrames}`]
        }
        return []
    }
    const inputFileClone = JSON.parse(JSON.stringify(options.inputFiles))
    const objClone = {
      inputFiles: inputFileClone,
      outputFormat: options.outputFormat,
      outputDir,
      optionAgrs: optionAgrs(),
      type: options.type
    }
    return await ipcRenderer.invoke('convert_media', objClone)
  }

  static async openOutputFolder(type?: string): Promise<void> {
    return await ipcRenderer.invoke('open_output_folder', type)
  }

  static async stopConvert(state: {job_id: string}): Promise<void> {
    return await ipcRenderer.invoke('cancel_conversion', state)
  }

}