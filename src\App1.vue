<template>
  <div class="container mx-auto p-4">
    <!-- File Input -->
    <div class="mb-4">
      <button 
        @click="selectVideo"
        class="px-4 py-2 bg-blue-500 text-white rounded"
      >
        Select Video
      </button>
      <span class="ml-2">{{ videoPath }}</span>
    </div>

    <!-- Video Preview -->
    <div class="relative w-full max-w-2xl mx-auto">
      <video
        ref="videoRef"
        class="w-full"
        controls
        @loadedmetadata="onVideoLoad"
      ></video>
      
      <!-- Drawing Canvas -->
      <canvas
        ref="canvasRef"
        @mousedown="startDrawing"
        @mousemove="draw"
        @mouseup="stopDrawing"
        class="absolute top-0 left-0 pointer-events-auto"
      ></canvas>
    </div>

    <!-- Controls -->
    <div class="mt-4 space-x-4">
      <button
        @click="resetCanvas"
        class="px-4 py-2 bg-red-500 text-white rounded"
      >
        Reset
      </button>
      <button
        @click="processVideo"
        :disabled="!canProcess"
        class="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
      >
        Remove Logo
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
//- import { mockConvertFileSrc } from "@tauri-apps/api/mocks";
//- import { open, save } from '@tauri-apps/plugin-dialog';
//- import * as invoke from '@tauri-apps/plugin-shell'
//- import { convertFileSrc,invoke  } from "@tauri-apps/api/core";

//- mockConvertFileSrc("windows")


const videoRef = ref<HTMLVideoElement>()
const canvasRef = ref<HTMLCanvasElement>()
const videoPath = ref('')
const isDrawing = ref(false)
const startPos = ref({ x: 0, y: 0 })
const rectangle = ref<{ x: number; y: number; w: number; h: number } | null>(null)

const canProcess = ref(false)

// Select video file
async function selectVideo() {
  try {
    const selected = await open({
      multiple: false,
      filters: [{
        name: 'Video',
        extensions: ['mp4', 'avi', 'mkv']
      }]
    })
    if (selected) {
    const url = convertFileSrc(selected)
      videoPath.value = selected as string
      if (videoRef.value) {
        const videoUrl = URL.createObjectURL(new Blob([await invoke('read_video_file', { path: selected })]))
        console.log(videoUrl,selected)
        videoRef.value.src = videoUrl as string
      }
    }
  } catch (err) {
    console.error('Error selecting file:', err)
  }
}

// Video loaded handler
function onVideoLoad() {
  if (videoRef.value && canvasRef.value) {
    canvasRef.value.width = videoRef.value.videoWidth
    canvasRef.value.height = videoRef.value.videoHeight
  }
}

// Drawing handlers
function startDrawing(e: MouseEvent) {
  isDrawing.value = true
  const rect = canvasRef.value?.getBoundingClientRect()
  if (rect) {
    startPos.value = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    }
  }
}

function draw(e: MouseEvent) {
  if (!isDrawing.value || !canvasRef.value) return

  const ctx = canvasRef.value.getContext('2d')
  const rect = canvasRef.value.getBoundingClientRect()
  
  if (ctx) {
    ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)
    
    const currentX = e.clientX - rect.left
    const currentY = e.clientY - rect.top
    
    const width = currentX - startPos.value.x
    const height = currentY - startPos.value.y
    
    ctx.strokeStyle = 'red'
    ctx.lineWidth = 2
    ctx.strokeRect(startPos.value.x, startPos.value.y, width, height)
    
    rectangle.value = {
      x: Math.min(startPos.value.x, currentX),
      y: Math.min(startPos.value.y, currentY),
      w: Math.abs(width),
      h: Math.abs(height)
    }
    
    canProcess.value = true
  }
}

function stopDrawing() {
  isDrawing.value = false
}

function resetCanvas() {
  if (canvasRef.value) {
    const ctx = canvasRef.value.getContext('2d')
    ctx?.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)
  }
  rectangle.value = null
  canProcess.value = false
}

// Process video with FFmpeg
async function processVideo() {
  if (!videoPath.value || !rectangle.value) return

  try {
    const savePath = await save({
      filters: [{
        name: 'Video',
        extensions: ['mp4']
      }]
    })

    if (savePath) {
      const result = await invoke('delogo_video', {
        input: videoPath.value,
        output: savePath,
        x: Math.round(rectangle.value.x),
        y: Math.round(rectangle.value.y),
        w: Math.round(rectangle.value.w),
        h: Math.round(rectangle.value.h)
      })
      
      console.log('Processing result:', result)
      alert('Video processed successfully!')
    }
  } catch (err) {
    console.error('Error processing video:', err)
    alert('Error processing video. Check console for details.')
  }
}
</script>
