const ffmpeg = require("fluent-ffmpeg");

const { promisify } = require("util");
// const streamPipeline = promisify(require('stream').pipeline);

const fs = require("fs").promises;
const path = require("path");
const os = require("os");

const tmpDir = os.tmpdir();
const uuidv4 = require("uuid").v4;

console.log("tmpDir", tmpDir);

async function checkFileAccess(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch (error) {
    return false;
  }
}

const getVideoInfo = async (event, filePath) => {
  if (!filePath) {
    throw new Error("No input specified");
  }
  S.filePath = "";
  S.filePath = filePath;
  // Check if file exists
  const fileExists = await checkFileAccess(filePath);
  if (!fileExists) {
    throw new Error(`File not found or not accessible: ${filePath}`);
  }

  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(new Error(`FFprobe error: ${err.message}`));
        return;
      }

      if (!metadata || !metadata.streams) {
        reject(new Error("Invalid video file or no metadata found"));
        return;
      }

      const videoStream = metadata.streams.find(
        (stream) => stream.codec_type === "video"
      );
      if (!videoStream) {
        reject(new Error("No video stream found in file"));
        return;
      }

      // Parse frame rate
      let fps = 0;
      if (videoStream.r_frame_rate) {
        const [num, den] = videoStream.r_frame_rate.split("/");
        fps = parseFloat(num) / parseFloat(den);
      }

      // Calculate total frames
      let totalFrames = parseInt(videoStream.nb_frames) || 0;
      if (!totalFrames && metadata.format.duration) {
        totalFrames = Math.ceil(fps * metadata.format.duration);
      }

      resolve({
        width: videoStream.width || null,
        height: videoStream.height || null,
        fps: fps,
        duration: metadata.format.duration || null,
        total_frames: totalFrames
      });
    });
  });
};

// Delogo video handler
const delogoVideo = async (
  event,
  { input, output, filters, videoInfo, detected }
) => {
  const filterFilePath = path.join(os.tmpdir(), "filter_script.txt");
  console.log("filterFilePath", filterFilePath, filters);

  // Write filter script
  await fs.writeFile(filterFilePath, filters);

  return new Promise(async (resolve, reject) => {
    const command = ffmpeg(input);

    // Set up progress handling
    let lastProgress = null;
    command.on("progress", (progress) => {
      const percentage = Math.min(99, Math.floor(progress.percent || 0));

      if (percentage !== lastProgress) {
        lastProgress = percentage;
        event.sender.send("ffmpeg-progress", percentage);
      }
    });
    const encoder = await getEncoder();
    console.log("encoder", encoder);
    // Set up FFmpeg command
    if (detected === "yes") {
      command
        .inputOptions(["-filter_complex_script", filterFilePath])
        .outputOptions(["-map", "[outv]", "-c:v", encoder]);
    } else {
      command.complexFilter(filters).outputOptions(["-map", "[outv]"]);

      if (filters.includes("[outa]")) {
        command.outputOptions(["-map", "[outa]", "-c:a", "aac"]);
      }

      command.outputOptions(["-c:v", encoder]);
    }

    // Execute FFmpeg command
    command
      .on("end", () => {
        event.sender.send("ffmpeg-progress", 100);
        resolve("Processing completed");
      })
      .on("error", (err) => {
        event.sender.send("ffmpeg-progress", -1);
        reject(err.message);
      })
      .on("stderr", (line) => {
        event.sender.send("ffmpeg-log", line);
      })
      .save(output);
  });
};

// Read video file handler
const readVideoFile = async (event, filePath) => {
  try {
    const data = await fs.readFile(filePath);
    return data;
  } catch (error) {
    throw error.message;
  }
};

// Create temp file handler
const createTempFile = async (event, filePath) => {
  try {
    const ext = path.extname(filePath);
    const fileName = `temp_${uuidv4()}${ext}`;
    const tempDir = path.join(os.tmpdir(), "delogo", "temp");

    await fs.mkdir(tempDir, { recursive: true });

    const tempPath = path.join(tempDir, fileName);
    await fs.copyFile(filePath, tempPath);

    return tempPath;
  } catch (error) {
    throw error.message;
  }
};

// Helper function to get appropriate encoder

const getEncoders = () => {
  return new Promise((resolve, reject) => {
    ffmpeg.getAvailableEncoders((err, encoders) => {
      if (err) reject(err);
      else resolve(encoders);
    });
  });
};
async function getEncoder() {
  try {
    const encoders = await getEncoders();
    if (encoders["h264_nvenc"]) return "h264_nvenc";
    if (encoders["h264_amf"]) return "h264_amf";
    return "libx264";
  } catch (error) {
    return "libx264";
  }
}

module.exports = {
  getVideoInfo,
  delogoVideo,
  readVideoFile,
  createTempFile
};
